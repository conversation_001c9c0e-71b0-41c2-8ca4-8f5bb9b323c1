/* 子页面共享样式 - 浪漫情侣网站 */

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'ZiXiaoHunGouYu', sans-serif;
    background: linear-gradient(135deg, #0c4a6e 0%, #075985 25%, #0369a1 50%, #0284c7 75%, #0ea5e9 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
    position: relative;
}

/* 星空背景 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, #fff, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, #fff, transparent),
        radial-gradient(1px 1px at 200px 60px, rgba(255,255,255,0.7), transparent),
        radial-gradient(2px 2px at 250px 20px, #fff, transparent),
        radial-gradient(1px 1px at 300px 90px, rgba(255,255,255,0.5), transparent);
    background-repeat: repeat;
    background-size: 350px 150px;
    animation: sparkle 25s linear infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes sparkle {
    from { transform: translateX(0); }
    to { transform: translateX(350px); }
}

/* 闪烁星星效果 */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(1px 1px at 15px 25px, rgba(255,255,255,0.9), transparent),
        radial-gradient(1px 1px at 85px 45px, rgba(255,255,255,0.7), transparent),
        radial-gradient(1px 1px at 145px 15px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 205px 75px, rgba(255,255,255,0.6), transparent),
        radial-gradient(1px 1px at 265px 35px, rgba(255,255,255,0.9), transparent),
        radial-gradient(1px 1px at 325px 65px, rgba(255,255,255,0.7), transparent),
        radial-gradient(1px 1px at 385px 25px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 445px 85px, rgba(255,255,255,0.6), transparent),
        radial-gradient(1px 1px at 55px 95px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 115px 125px, rgba(255,255,255,0.6), transparent),
        radial-gradient(1px 1px at 175px 55px, rgba(255,255,255,0.9), transparent),
        radial-gradient(1px 1px at 235px 105px, rgba(255,255,255,0.7), transparent),
        radial-gradient(1px 1px at 295px 75px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 355px 115px, rgba(255,255,255,0.6), transparent),
        radial-gradient(1px 1px at 415px 45px, rgba(255,255,255,0.9), transparent),
        radial-gradient(1px 1px at 475px 95px, rgba(255,255,255,0.7), transparent);
    background-repeat: repeat;
    background-size: 500px 140px;
    animation: twinkle 6s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes twinkle {
    0%, 100% { opacity: 0.2; }
    12.5% { opacity: 0.9; }
    25% { opacity: 0.4; }
    37.5% { opacity: 1; }
    50% { opacity: 0.3; }
    62.5% { opacity: 0.8; }
    75% { opacity: 0.5; }
    87.5% { opacity: 0.95; }
}

/* 增强流星效果 */
.shooting-stars {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 2;
}

.shooting-star {
    position: absolute;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, #fff 0%, rgba(255,255,255,0.8) 30%, transparent 70%);
    border-radius: 50%;
    box-shadow: 0 0 10px #fff, 0 0 20px #fff, 0 0 30px #fff;
    animation-name: shoot;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
}

.shooting-star::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 2px;
    background: linear-gradient(90deg, rgba(255,255,255,0.9), rgba(255,255,255,0.5), transparent);
    transform: translate(-100px, -50%);
    border-radius: 2px;
    box-shadow: 0 0 5px rgba(255,255,255,0.8);
}

/* 流星随机位置 - 改进的随机效果 */
.shooting-star:nth-child(1) {
    top: 8%;
    left: 12%;
    animation-delay: 0.3s;
    animation-duration: 4.2s;
}

.shooting-star:nth-child(2) {
    top: 23%;
    left: 78%;
    animation-delay: 2.1s;
    animation-duration: 3.8s;
}

.shooting-star:nth-child(3) {
    top: 41%;
    left: 31%;
    animation-delay: 3.7s;
    animation-duration: 4.9s;
}

.shooting-star:nth-child(4) {
    top: 17%;
    left: 89%;
    animation-delay: 5.3s;
    animation-duration: 3.4s;
}

.shooting-star:nth-child(5) {
    top: 62%;
    left: 19%;
    animation-delay: 6.8s;
    animation-duration: 4.6s;
}

.shooting-star:nth-child(6) {
    top: 34%;
    left: 67%;
    animation-delay: 8.2s;
    animation-duration: 3.9s;
}

.shooting-star:nth-child(7) {
    top: 79%;
    left: 43%;
    animation-delay: 9.6s;
    animation-duration: 4.1s;
}

.shooting-star:nth-child(8) {
    top: 56%;
    left: 82%;
    animation-delay: 11.4s;
    animation-duration: 5.2s;
}

.shooting-star:nth-child(9) {
    top: 91%;
    left: 26%;
    animation-delay: 12.9s;
    animation-duration: 3.7s;
}

.shooting-star:nth-child(10) {
    top: 14%;
    left: 58%;
    animation-delay: 14.3s;
    animation-duration: 4.4s;
}

@keyframes shoot {
    0% {
        transform: translateX(0) translateY(0) rotate(45deg) scale(0.5);
        opacity: 0;
    }
    10% {
        opacity: 1;
        transform: translateX(50px) translateY(50px) rotate(45deg) scale(1);
    }
    90% {
        opacity: 0.8;
        transform: translateX(350px) translateY(350px) rotate(45deg) scale(1.2);
    }
    100% {
        transform: translateX(400px) translateY(400px) rotate(45deg) scale(0.3);
        opacity: 0;
    }
}

/* 浪漫漂浮元素动画 */
.hearts-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;
}

/* 爱心样式 */
.heart {
    position: absolute;
    color: rgba(255, 182, 193, 0.8);
    font-size: 20px;
    animation: float 8s infinite linear;
}

.heart::before {
    content: '💕';
}

/* 不同类型的浪漫元素 */
.heart.type-1::before { content: '💕'; color: rgba(255, 182, 193, 0.9); }
.heart.type-2::before { content: '💖'; color: rgba(255, 105, 180, 0.8); }
.heart.type-3::before { content: '💗'; color: rgba(255, 20, 147, 0.7); }
.heart.type-4::before { content: '💓'; color: rgba(255, 69, 0, 0.6); }
.heart.type-5::before { content: '💝'; color: rgba(255, 140, 0, 0.8); }
.heart.type-6::before { content: '💘'; color: rgba(255, 192, 203, 0.9); }
.heart.type-7::before { content: '💞'; color: rgba(255, 160, 122, 0.7); }
.heart.type-8::before { content: '💟'; color: rgba(255, 218, 185, 0.8); }
.heart.type-9::before { content: '❤️'; color: rgba(220, 20, 60, 0.8); }
.heart.type-10::before { content: '🌹'; color: rgba(255, 0, 0, 0.7); }
.heart.type-11::before { content: '🌸'; color: rgba(255, 182, 193, 0.8); }
.heart.type-12::before { content: '✨'; color: rgba(255, 215, 0, 0.9); }

/* 不同大小 */
.heart.small { font-size: 16px; }
.heart.medium { font-size: 24px; }
.heart.large { font-size: 32px; }

/* 位置分布 - 更多数量 */
.heart:nth-child(1) { left: 5%; animation-delay: 0s; animation-duration: 7s; }
.heart:nth-child(2) { left: 15%; animation-delay: 1s; animation-duration: 9s; }
.heart:nth-child(3) { left: 25%; animation-delay: 2s; animation-duration: 6s; }
.heart:nth-child(4) { left: 35%; animation-delay: 3s; animation-duration: 8s; }
.heart:nth-child(5) { left: 45%; animation-delay: 4s; animation-duration: 7s; }
.heart:nth-child(6) { left: 55%; animation-delay: 5s; animation-duration: 9s; }
.heart:nth-child(7) { left: 65%; animation-delay: 6s; animation-duration: 6s; }
.heart:nth-child(8) { left: 75%; animation-delay: 7s; animation-duration: 8s; }
.heart:nth-child(9) { left: 85%; animation-delay: 8s; animation-duration: 7s; }
.heart:nth-child(10) { left: 95%; animation-delay: 9s; animation-duration: 9s; }
.heart:nth-child(11) { left: 10%; animation-delay: 10s; animation-duration: 6s; }
.heart:nth-child(12) { left: 30%; animation-delay: 11s; animation-duration: 8s; }
.heart:nth-child(13) { left: 50%; animation-delay: 12s; animation-duration: 7s; }
.heart:nth-child(14) { left: 70%; animation-delay: 13s; animation-duration: 9s; }
.heart:nth-child(15) { left: 90%; animation-delay: 14s; animation-duration: 6s; }

@keyframes float {
    0% {
        transform: translateY(100vh) rotate(0deg) scale(0.5);
        opacity: 0;
    }
    10% {
        opacity: 1;
        transform: translateY(90vh) rotate(10deg) scale(1);
    }
    50% {
        transform: translateY(50vh) rotate(180deg) scale(1.2);
        opacity: 0.8;
    }
    90% {
        opacity: 0.6;
        transform: translateY(10vh) rotate(350deg) scale(0.8);
    }
    100% {
        transform: translateY(-10vh) rotate(360deg) scale(0.3);
        opacity: 0;
    }
}

/* 主容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 2;
}

/* 页面头部 */
.page-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 20px;
    background: linear-gradient(135deg,
        rgba(255, 182, 193, 0.9) 0%,
        rgba(255, 192, 203, 0.8) 25%,
        rgba(255, 218, 185, 0.9) 50%,
        rgba(255, 228, 225, 0.8) 75%,
        rgba(255, 240, 245, 0.9) 100%);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(255, 105, 180, 0.3),
                0 0 30px rgba(255, 182, 193, 0.2);
    /* 移除磨砂效果，让背景完全透明 */
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
    animation: sectionGlow 6s ease-in-out infinite;
}

.page-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%);
    animation: shimmer 5s linear infinite;
    pointer-events: none;
}

.page-title {
    font-family: 'Playfair Display', 'ZiXiaoHunGouYu', serif;
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.5rem;
    color: #666;
    font-weight: 500;
    margin-bottom: 20px;
}

.page-icon {
    font-size: 3rem;
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    display: block;
}

/* 返回按钮 */
.back-button {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, #48cae4 0%, #0077b6 100%);
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(72, 202, 228, 0.4);
    margin-bottom: 30px;
}

.back-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(72, 202, 228, 0.6);
    text-decoration: none;
    color: white;
}

/* 内容区域 - 浪漫动态背景 */
.content-section {
    border-radius: 20px;
    padding: 40px 20px !important;
    margin-bottom: 30px;
    /* 移除磨砂效果，让背景完全透明 */
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
    animation: sectionGlow 6s ease-in-out infinite;
}

/* 不同粉色系变化 */
.content-section:nth-child(odd) {
    background: transparent; /* 完全透明，让花朵背景完全显示 */
    box-shadow: 0 15px 35px rgba(255, 182, 193, 0.25),
                0 0 25px rgba(255, 192, 203, 0.2);
}

.content-section:nth-child(even) {
    background: transparent; /* 完全透明，让花朵背景完全显示 */
    box-shadow: 0 15px 35px rgba(255, 218, 185, 0.25),
                0 0 25px rgba(255, 228, 196, 0.2);
}

@keyframes sectionGlow {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.01);
    }
}

.section-title {
    font-family: 'Playfair Display', 'ZiXiaoHunGouYu', serif;
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8);
}

.section-content {
    line-height: 1.8;
    font-size: 1.1rem;
    color: #2c3e50;
    text-align: center;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* 时间线样式 */
.timeline {
    position: relative;
    max-width: 2000px;
    margin: 0 auto;
}

.timeline::after {
    content: '';
    position: absolute;
    width: 6px;
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -3px;
    border-radius: 3px;
    animation: timelinePulse 3s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
}

@keyframes timelinePulse {
    0% {
        background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
        box-shadow: 0 0 10px rgba(255, 107, 107, 0.4);
        opacity: 0.8;
        transform: scaleY(1);
    }
    12.5% {
        opacity: 1;
        background: linear-gradient(135deg, #ff8a80 0%, #ffcc80 100%);
        box-shadow: 0 0 12px rgba(255, 138, 128, 0.5);
        transform: scaleY(1.01);
    }
    25% {
        background: linear-gradient(135deg, #48cae4 0%, #0077b6 100%);
        box-shadow: 0 0 15px rgba(72, 202, 228, 0.6);
        opacity: 1;
        transform: scaleY(1.02);
    }
    37.5% {
        opacity: 0.9;
        background: linear-gradient(135deg, #64b5f6 0%, #1976d2 100%);
        box-shadow: 0 0 18px rgba(100, 181, 246, 0.7);
        transform: scaleY(1.03);
    }
    50% {
        background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%);
        box-shadow: 0 0 20px rgba(255, 149, 0, 0.7);
        opacity: 1;
        transform: scaleY(1.04);
    }
    62.5% {
        opacity: 0.9;
        background: linear-gradient(135deg, #ffab40 0%, #ff7043 100%);
        box-shadow: 0 0 18px rgba(255, 171, 64, 0.6);
        transform: scaleY(1.03);
    }
    75% {
        background: linear-gradient(135deg, #a8e6cf 0%, #88d8c0 100%);
        box-shadow: 0 0 15px rgba(168, 230, 207, 0.6);
        opacity: 1;
        transform: scaleY(1.02);
    }
    87.5% {
        opacity: 0.9;
        background: linear-gradient(135deg, #b2dfdb 0%, #4db6ac 100%);
        box-shadow: 0 0 12px rgba(178, 223, 219, 0.5);
        transform: scaleY(1.01);
    }
    100% {
        background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
        box-shadow: 0 0 10px rgba(255, 107, 107, 0.4);
        opacity: 0.8;
        transform: scaleY(1);
    }
}

.timeline-item {
    padding: 15px 40px;
    position: relative;
    background-color: inherit;
    width: 50%;
    margin-bottom: 5px;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.timeline-item::after {
    content: '';
    position: absolute;
    width: 24px;
    height: 24px;
    right: -12px;
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
    border-radius: 50%;
    top: 35px;
    box-shadow: 0 0 0 4px white, 0 0 0 8px rgba(255, 107, 107, 0.3);
    animation: dotPulse 2s ease-in-out infinite;
    animation-delay: calc(var(--item-index) * 0.3s);
}

@keyframes dotPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 4px white, 0 0 0 8px rgba(255, 107, 107, 0.3);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 0 4px white, 
                    0 0 0 12px rgba(72, 202, 228, 0.4),
                    0 0 20px rgba(255, 107, 107, 0.6);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 4px white, 0 0 0 8px rgba(255, 107, 107, 0.3);
    }
}

.timeline-item:nth-child(even),
.timeline-item.right {
    left: 50%;
}

.timeline-item:nth-child(even)::after,
.timeline-item.right::after {
    left: -12px;
}

/* 时光轴操作按钮样式 */
.timeline-actions {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 8px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 10;
}

.timeline-item:hover .timeline-actions {
    opacity: 1;
    transform: translateY(0);
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.edit-btn {
    background: transparent;
    color: #48cae4;
    border: 2px solid rgba(72, 202, 228, 0.6);
}

.edit-btn:hover {
    background: rgba(72, 202, 228, 0.2);
    color: #0077b6;
    transform: scale(1.1) rotate(10deg);
    box-shadow: 0 6px 20px rgba(72, 202, 228, 0.4);
    border: 2px solid rgba(0, 119, 182, 0.8);
}

.delete-btn {
    background: transparent;
    color: #ff6b6b;
    border: 2px solid rgba(255, 107, 107, 0.6);
}

.delete-btn:hover {
    background: rgba(255, 107, 107, 0.2);
    color: #ee5a52;
    transform: scale(1.1) rotate(-10deg);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
    border: 2px solid rgba(238, 90, 82, 0.8);
}

.timeline-content {
    padding: 40px 70px !important;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(240, 248, 255, 0.95) 25%,
        rgba(255, 250, 240, 0.95) 50%,
        rgba(248, 255, 248, 0.95) 75%,
        rgba(255, 240, 245, 0.95) 100%);
    position: relative;
    border-radius: 25px;
    box-shadow: 0 15px 40px rgba(255, 105, 180, 0.25),
                0 5px 15px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(255, 255, 255, 0.4);
    overflow: hidden;
    animation: cardGlow 5s ease-in-out infinite;
    min-height: 180px;
    transform: scale(1);
    transition: all 0.4s ease;
}

.timeline-content:hover {
    transform: scale(1.02);
    box-shadow: 0 20px 50px rgba(255, 105, 180, 0.3),
                0 8px 25px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.timeline-content::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%);
    animation: shimmer 4s linear infinite;
    pointer-events: none;
}

.timeline-date {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.3rem;
    color: #ff6b6b;
    font-weight: 600;
    margin-bottom: 12px;
}

.timeline-title {
    font-family: 'Playfair Display', 'ZiXiaoHunGouYu', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.timeline-description {
    color: #666;
    line-height: 1.7;
    font-size: 1.05rem;
}

/* 卡片发光动画 */
@keyframes cardGlow {
    0%, 100% {
        box-shadow: 0 15px 40px rgba(255, 105, 180, 0.25),
                    0 5px 15px rgba(0, 0, 0, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.8);
    }
    50% {
        box-shadow: 0 20px 50px rgba(255, 105, 180, 0.3),
                    0 8px 25px rgba(0, 0, 0, 0.15),
                    inset 0 1px 0 rgba(255, 255, 255, 0.9);
    }
}

/* 网格布局 */
.memory-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.memory-card {
    background: transparent; /* 完全透明，让花朵背景完全显示 */
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(255, 105, 180, 0.3),
                0 0 25px rgba(255, 182, 193, 0.2);
    /* 移除磨砂效果，让背景完全透明 */
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    animation: cardGlow 5s ease-in-out infinite;
    opacity: 0;
    transform: translateY(30px) scale(0.9);
}

/* 美好瞬间操作按钮 */
.memory-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    gap: 6px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 10;
}

.memory-card:hover .memory-actions {
    opacity: 1;
    transform: translateY(0);
}

.memory-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.15) 50%,
        transparent 70%);
    animation: shimmer 4s linear infinite;
    pointer-events: none;
}

.memory-card:hover {
    transform: translateY(-5px);
}

.memory-card-icon {
    font-size: 3rem;
    background: linear-gradient(135deg, #ff9500 0%, #ff6b6b 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
}

.memory-card-title {
    font-family: 'Playfair Display', 'ZiXiaoHunGouYu', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.memory-card-content {
    color: #666;
    line-height: 1.6;
}

/* 引用样式 */
.quote-section {
    text-align: center;
    padding: 40px;
    background: transparent; /* 完全透明，让花朵背景完全显示 */
    border-radius: 15px;
    margin: 30px 0;
}

.quote-text {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.8rem;
    color: #ff6b6b;
    font-style: italic;
    margin-bottom: 15px;
}

.quote-author {
    font-size: 1rem;
    color: #888;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }
    
    .page-subtitle {
        font-size: 1.3rem;
    }
    
    .content-section {
        padding: 25px;
    }
    
    .timeline::after {
        left: 31px;
    }
    
    .timeline-item {
        width: 100%;
        padding-left: 70px;
        padding-right: 25px;
    }
    
    .timeline-item::after {
        left: 21px;
    }
    
    .timeline-item:nth-child(even),
    .timeline-item.right {
        left: 0%;
    }
    
    .timeline-item:nth-child(even)::after,
    .timeline-item.right::after {
        left: 21px;
    }
    
    /* 移动端时光轴操作按钮 */
    .timeline-actions {
        top: 10px;
        right: 10px;
        opacity: 1;
        transform: translateY(0);
    }
    
    .action-btn {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }
    
    .memory-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 15px;
    }
    
    .page-header {
        padding: 30px 15px;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .content-section {
        padding: 20px;
    }
}
