# Love 项目 AI 快速指南

## 💕 项目概述

Love 项目是 Yu & Wang 专属的情侣网站。

### 核心特性
- 💌 **留言板系统**: 支持 Yu/Wang/Other 三身份留言
- 📅 **纪念日管理**: 生日倒计时、恋爱天数计算
- 💑 **回忆记录**: 四个浪漫回忆卡片展示
- 🎨 **现代化UI**: 渐变字体、响应式设计、浪漫动画

## 📁 项目结构

```
love/                               # 💕 Love项目根目录
├── 📄 guide-love.md                # 本文件 - AI快速指南
├── 📄 README.md                    # 项目详细文档
├── 📄 USAGE.md                     # 使用指南
├── 📄 deploy-love.sh               # 标准化部署脚本（由主deploy.sh调用）
├── 📄 manage.sh                    # 统一管理工具（推荐）
├── 📄 server.js                    # Node.js后端服务
├── 📄 package.json                 # Node.js项目配置
├── 📄 index.html                   # 主页面（已废弃，使用html/目录）
├── 📄 style.css                    # 主样式文件
├── 📄 pages.css                    # 页面样式文件
├── 📄 script.js                    # 前端脚本
├── 📄 romantic-quotes.js           # 浪漫话语数据
├── 📂 html/                        # 前端页面目录
│   ├── index.html                  # 主页（Love计数器）
│   ├── together-days.html          # 在一起的日子
│   ├── anniversary.html            # 纪念日页面
│   ├── meetings.html               # 相遇记录
│   └── memorial.html               # 纪念页面
├── 📂 data/                        # 数据目录
│   ├── love_messages.db            # SQLite数据库
│   └── backups/                    # 数据库备份
├── 📂 test/                        # 测试文件
│   └── test-api.html               # API测试页面
├── 📂 logs/                        # 日志文件
│   └── backend.log                 # 后端日志
├── 📂 background/                  # 背景资源
│   ├── home/                       # 首页背景视频 (home.mp4)
│   ├── together-days/              # 在一起的日子背景视频 (together-days.mp4)
│   ├── anniversary/                # 纪念日背景视频 (anniversary.mp4)
│   ├── meetings/                   # 相遇记录背景视频 (meetings.mp4)
│   └── memorial/                   # 纪念页面背景视频 (memorial.mp4)
├── 📂 fonts/                       # 字体文件
└── 📂 scripts/                     # 预留脚本目录
```

## 🌐 网络配置架构（重要）

### 域名配置 - 宝塔面板部署
Love项目通过**宝塔面板**部署，使用独立域名访问，支持以下访问路径：

```
https://love.yuh.cool/
├── /                               → Love网站主页 ✅
├── /together-days                  → 在一起的日子页面
├── /anniversary                    → 纪念日页面
├── /meetings                       → 相遇记录页面
├── /memorial                       → 纪念页面
├── /api/                           → Love API服务 (端口1314)
├── /api/messages                   → 留言系统API
├── /verify                         → 测试验证页面 (用于所有测试)
├── /style.css                      → 样式文件
├── /pages.css                      → 页面样式
├── /script.js                      → 前端脚本
├── /romantic-quotes.js             → 浪漫话语
└── /background/                    → 背景资源
    ├── /home/<USER>
    ├── /together-days/             → 在一起的日子背景视频
    ├── /anniversary/               → 纪念日背景视频
    ├── /meetings/                  → 相遇记录背景视频
    └── /memorial/                  → 纪念页面背景视频
```

### 宝塔面板配置管理
Love项目通过宝塔面板进行部署和管理：
- **域名绑定**: love.yuh.cool → 完整指向 localhost:1314
- **SSL证书**: 通过宝塔面板自动申请和管理Let's Encrypt证书
- **反向代理**: 宝塔面板配置反向代理，将域名请求转发到内部端口1314
- **静态文件**: 直接通过Nginx服务静态资源（CSS、JS、背景视频等）

### 端口和服务
- **Love API服务**: 内部端口1314 → 外部443端口（HTTPS）
- **域名访问**: love.yuh.cool → localhost:1314
- **SSL证书**: 通过宝塔面板自动管理

## 🎨 功能模块详解

### 1. 💖 恋爱天数实时计算器
- **功能**: 从恋爱开始到现在的精确天数、小时数、分钟数
- **技术**: JavaScript setInterval每分钟更新
- **页面**: `https://love.yuh.cool/` (主页)
- **样式**: 渐变色数字 + Playfair Display字体

### 2. 🎂 生日倒计时系统
- **Yu的生日**: 01月16日（农历腊月初八）
- **Wang的生日**: 04月15日
- **样式**: 个性化渐变色（Yu: 紫蓝色, Wang: 粉紫色）
- **页面**: `https://love.yuh.cool/anniversary`

### 3. 💌 三身份留言系统
- **身份**: Yu / Wang / Other（访客）
- **功能**: 发布留言、查看留言、按身份筛选
- **API**: `https://love.yuh.cool/api/messages`
- **数据库**: SQLite (`data/love_messages.db`)

### 4. 📖 回忆卡片系统
- **四个主题**: 第一次相遇💫 / 第一次牵手🤝 / 第一个礼物🎁 / 在一起的日子💕
- **页面**: `https://love.yuh.cool/meetings`, `https://love.yuh.cool/memorial`
- **交互**: 模态框详情展示

### 5. 🎨 浪漫页面
- **在一起的日子**: `https://love.yuh.cool/together-days` - 恋爱历程展示
- **纪念页面**: `https://love.yuh.cool/memorial` - 特殊纪念内容

## 🛠️ 技术架构

### 后端技术栈
- **Node.js + Express**: Web服务器框架
- **SQLite3**: 轻量级数据库
- **systemd**: 服务管理（love-site.service）
- **端口**: 1314（避免常见端口冲突）

### 前端技术栈
- **HTML5**: 语义化结构 + 响应式设计
- **CSS3**: Flexbox/Grid布局 + 渐变色 + 动画
- **JavaScript**: ES6+ 语法 + 模块化设计
- **字体**: Google Fonts (Dancing Script, Playfair Display, Inter)

### 数据库设计
```sql
-- love_messages_new 表结构
CREATE TABLE love_messages_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    author TEXT NOT NULL CHECK(author IN ('Yu', 'Wang', 'Other')),
    content TEXT NOT NULL,
    created_timestamp INTEGER NOT NULL,
    updated_timestamp INTEGER NOT NULL,
    beijing_date TEXT NOT NULL,
    beijing_datetime TEXT NOT NULL,
    client_ip TEXT DEFAULT '',
    status TEXT DEFAULT 'active',
    version INTEGER DEFAULT 1
);
```

## 🚀 部署方式（AI操作指南）

### 1. 融合项目部署（推荐）
```bash
# 在workspace根目录执行
sudo ./deploy.sh                 # 完整部署所有项目
sudo ./deploy.sh --love          # 仅部署Love项目
```

### 2. 独立项目部署
```bash
cd love
./deploy-love.sh                 # 标准化部署脚本（推荐）
# 或
./manage.sh start                # 通过管理工具启动
```

### 3. 快速启动
```bash
cd love
./manage.sh                      # 交互式管理界面
./manage.sh start                # 启动所有服务
./manage.sh status               # 查看服务状态
```

## 🛠️ 管理命令（AI必知）

### 统一管理工具（优先使用）
```bash
cd love
./manage.sh                             # 交互式管理界面
./manage.sh start                       # 启动所有服务
./manage.sh stop                        # 停止所有服务
./manage.sh restart                     # 重启所有服务
./manage.sh status                      # 查看系统状态
./manage.sh logs                        # 查看日志
```

### 管理功能模块
1. **📊 状态监控** - 实时查看服务、数据库、SSL状态
2. **⚙️ 服务管理** - systemd服务控制、开机自启管理
3. **💾 数据库管理** - 备份、恢复、统计、维护
4. **🚀 部署管理** - 生产环境部署、完整Nginx配置（包含New-API+Love）
5. **🔍 系统验证** - 验证所有服务状态、测试外部访问
6. **📊 监控管理** - 性能监控、错误分析
7. **🔧 故障排除** - 自动诊断和修复
8. **📁 项目管理** - 文件清理、结构优化

### 宝塔面板管理（重要功能）
```bash
cd love
./manage.sh                             # 进入管理界面
# 选择: 部署管理 → 配置服务部署         # 配置Love项目的服务部署
# 选择: 系统验证 → 验证服务状态         # 检查Love服务运行状态
# 选择: 系统验证 → 测试外部访问         # 测试love.yuh.cool域名访问
```

## 🔌 API接口

### 留言系统API
```http
GET    https://love.yuh.cool/api/messages              # 获取所有留言
POST   https://love.yuh.cool/api/messages              # 创建新留言
PUT    https://love.yuh.cool/api/messages/:id          # 更新留言
DELETE https://love.yuh.cool/api/messages/:id          # 删除留言
GET    https://love.yuh.cool/api/messages/paginated    # 分页获取留言
GET    https://love.yuh.cool/api/messages/stats        # 留言统计
GET    https://love.yuh.cool/api/health                # 健康检查
```

### API测试
```bash
# 创建留言
curl -X POST https://love.yuh.cool/api/messages \
  -H "Content-Type: application/json" \
  -d '{"author":"Yu","content":"我爱你 💕"}'

# 获取留言
curl https://love.yuh.cool/api/messages

# 健康检查
curl https://love.yuh.cool/api/health

# 测试页面访问
curl https://love.yuh.cool/verify
```

## 💾 数据管理

### 数据库位置和备份
- **主数据库**: `data/love_messages.db`
- **备份目录**: `data/backups/`
- **自动备份**: 通过manage.sh管理工具

### 备份管理
```bash
cd love
./manage.sh                            # 进入管理界面
# 选择: 数据库管理 → 备份数据库
# 选择: 数据库管理 → 恢复数据库
# 选择: 数据库管理 → 管理备份文件
# 选择: 数据库管理 → 数据库统计
```

### 手动数据库操作
```bash
# 查看数据库表
sqlite3 data/love_messages.db ".tables"

# 查看留言统计
sqlite3 data/love_messages.db "SELECT author, COUNT(*) FROM love_messages_new GROUP BY author;"

# 备份数据库
cp data/love_messages.db data/backups/love_messages_$(date +%Y%m%d_%H%M%S).db
```

## 🔧 服务管理

### systemd服务
```bash
# 服务状态
sudo systemctl status love-site

# 启动/停止/重启
sudo systemctl start love-site
sudo systemctl stop love-site
sudo systemctl restart love-site

# 开机自启动
sudo systemctl enable love-site
sudo systemctl disable love-site
```

### 进程管理
```bash
# 查看Node.js进程
ps aux | grep "node server.js"

# 查看端口占用
ss -tuln | grep 1314
netstat -tlnp | grep 1314

# 手动启动后端（调试用）
cd love && node server.js
```

## 🔧 故障排除（AI诊断指南）

### 常见问题诊断
1. **服务无法启动**
   ```bash
   cd love
   ./manage.sh status                   # 检查服务状态
   ./manage.sh logs                     # 查看日志
   ps aux | grep "node server.js"      # 检查进程
   ss -tuln | grep 1314                # 检查端口
   ```

2. **网站无法访问**
   ```bash
   cd love
   ./manage.sh                         # 进入管理界面
   # 选择: 系统验证 → 测试外部访问
   # 选择: 故障排除 → 修复Nginx配置
   
   # 手动检查
   sudo nginx -t                       # 检查Nginx配置
   sudo systemctl status nginx        # 检查Nginx状态
   curl -I https://love.yuh.cool/      # 测试访问
   ```

3. **留言功能异常**
   ```bash
   # 测试API
   curl http://localhost:1314/api/health
   curl http://localhost:1314/api/messages
   
   # 检查数据库
   sqlite3 data/love_messages.db ".tables"
   sqlite3 data/love_messages.db "SELECT COUNT(*) FROM love_messages_new;"
   ```

4. **SSL证书问题**
   ```bash
   sudo certbot certificates            # 检查证书状态
   sudo certbot renew --dry-run         # 测试证书续期
   ```

### 日志查看
```bash
cd love
./manage.sh logs                       # 应用日志（推荐）
tail -f logs/backend.log               # 后端日志
sudo journalctl -u love-site -f        # systemd服务日志
sudo tail -f /var/log/nginx/error.log  # Nginx错误日志
```

### 自动故障修复
```bash
cd love
./manage.sh                           # 进入管理界面
# 选择: 故障排除 → 修复后端服务
# 选择: 故障排除 → 修复Nginx配置
# 选择: 故障排除 → 重启所有服务
```

## 🎯 AI助手操作要点

### 推荐操作流程
1. **状态检查**: `cd love && ./manage.sh status`
2. **服务管理**: 优先使用 `manage.sh` 统一工具
3. **宝塔配置**: 通过宝塔面板管理域名绑定和SSL证书
4. **外部测试**: 使用"系统验证 → 测试外部访问"验证love.yuh.cool访问
5. **故障排查**: 使用内置的故障排除功能自动修复

### 项目特点
- **独立域名**: love.yuh.cool 专属域名访问
- **端口**: 1314（内部服务端口）
- **宝塔部署**: 通过宝塔面板管理域名和SSL证书
- **独立项目**: 专注于情侣网站功能，不涉及其他系统配置
- **管理**: 所有功能集中在 `manage.sh`
- **数据**: SQLite数据库，支持完整备份恢复

### 宝塔面板集成
- **域名管理**: 通过宝塔面板绑定 love.yuh.cool 域名
- **SSL证书**: 宝塔面板自动申请和续期Let's Encrypt证书
- **反向代理**: 宝塔配置反向代理，将域名请求转发到localhost:1314
- **静态资源**: 直接通过Nginx服务CSS、JS、视频等静态文件
- **访问日志**: 通过宝塔面板查看访问日志和统计

### 关键管理功能
- **服务监控**: 实时监控Love服务运行状态
- **数据库管理**: 完整的备份恢复和统计功能
- **外部访问测试**: 测试所有页面的外部访问（使用 /verify 路径）
- **故障自动修复**: 自动诊断和修复常见问题

### 开发和修改
- **前端文件**: 主要在 `html/` 目录下的页面文件
- **样式文件**: `style.css` (主样式) 和 `pages.css` (页面样式)
- **脚本文件**: `script.js` (前端逻辑) 和 `romantic-quotes.js` (数据)
- **后端文件**: `server.js` (Express服务器和API)
- **配置文件**: `nginx-love/` 目录下的Nginx配置

---

**💕 提示**: Love项目是独立部署的情侣网站，通过宝塔面板管理域名和SSL证书。优先使用 `manage.sh` 进行所有管理操作。