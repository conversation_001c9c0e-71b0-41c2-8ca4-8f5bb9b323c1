<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代爱情话语数据测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .quote-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-style: italic;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>现代爱情话语数据加载测试</h1>
    
    <div class="test-section">
        <h2>数据加载状态</h2>
        <div id="loadStatus" class="status info">检查中...</div>
        <div id="dataInfo"></div>
    </div>

    <div class="test-section">
        <h2>功能测试</h2>
        <button onclick="testRandomQuote()">获取随机话语</button>
        <button onclick="testAllCategories()">测试所有分类</button>
        <div id="quoteDisplay" class="quote-display">点击按钮测试功能</div>
    </div>

    <div class="test-section">
        <h2>详细信息</h2>
        <div id="detailInfo"></div>
    </div>

    <!-- 引入现代爱情话语数据 -->
    <script src="/love/modern-quotes-data.js"></script>
    
    <script>
        // 检查数据加载状态
        function checkDataStatus() {
            const statusDiv = document.getElementById('loadStatus');
            const dataInfoDiv = document.getElementById('dataInfo');
            const detailInfoDiv = document.getElementById('detailInfo');
            
            if (typeof modernLoveQuotes === 'undefined') {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 数据加载失败：modernLoveQuotes 未定义';
                return false;
            }
            
            if (!Array.isArray(modernLoveQuotes)) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 数据格式错误：modernLoveQuotes 不是数组';
                return false;
            }
            
            if (modernLoveQuotes.length === 0) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 数据为空：modernLoveQuotes 数组长度为0';
                return false;
            }
            
            // 数据加载成功
            statusDiv.className = 'status success';
            statusDiv.textContent = '✅ 数据加载成功';
            
            // 显示数据信息
            dataInfoDiv.innerHTML = `
                <p><strong>数据总数：</strong>${modernLoveQuotes.length} 条</p>
                <p><strong>数据类型：</strong>${typeof modernLoveQuotes}</p>
                <p><strong>加载时间：</strong>${new Date().toLocaleTimeString()}</p>
            `;
            
            // 分析数据结构
            const categories = {};
            const sources = {};
            
            modernLoveQuotes.forEach(quote => {
                if (quote.category) {
                    categories[quote.category] = (categories[quote.category] || 0) + 1;
                }
                if (quote.source) {
                    sources[quote.source] = (sources[quote.source] || 0) + 1;
                }
            });
            
            detailInfoDiv.innerHTML = `
                <h3>分类统计：</h3>
                <ul>${Object.entries(categories).map(([cat, count]) => `<li>${cat}: ${count}条</li>`).join('')}</ul>
                <h3>来源统计：</h3>
                <ul>${Object.entries(sources).map(([src, count]) => `<li>${src}: ${count}条</li>`).join('')}</ul>
                <h3>示例数据：</h3>
                <pre>${JSON.stringify(modernLoveQuotes[0], null, 2)}</pre>
            `;
            
            return true;
        }
        
        // 测试随机话语功能
        function testRandomQuote() {
            if (!checkDataStatus()) return;
            
            const randomIndex = Math.floor(Math.random() * modernLoveQuotes.length);
            const quote = modernLoveQuotes[randomIndex];
            
            document.getElementById('quoteDisplay').innerHTML = `
                <strong>随机话语 #${randomIndex + 1}：</strong><br>
                "${quote.content}"<br>
                <small>— ${quote.source || '未知来源'} (${quote.category || '未分类'})</small>
            `;
        }
        
        // 测试所有分类
        function testAllCategories() {
            if (!checkDataStatus()) return;
            
            const categories = {};
            modernLoveQuotes.forEach(quote => {
                if (quote.category) {
                    if (!categories[quote.category]) {
                        categories[quote.category] = [];
                    }
                    categories[quote.category].push(quote);
                }
            });
            
            let result = '<strong>各分类示例：</strong><br>';
            Object.entries(categories).forEach(([cat, quotes]) => {
                const example = quotes[0];
                result += `<br><strong>${cat}类 (${quotes.length}条)：</strong><br>"${example.content}"<br>`;
            });
            
            document.getElementById('quoteDisplay').innerHTML = result;
        }
        
        // 页面加载完成后检查状态
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkDataStatus, 100);
        });
    </script>
</body>
</html>
