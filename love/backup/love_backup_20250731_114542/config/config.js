const path = require('path');
const fs = require('fs');

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, '.env') });

/**
 * Love项目配置管理模块
 * 统一管理所有配置项，支持环境变量覆盖
 */

// 配置验证函数
function validateConfig() {
    const errors = [];
    
    // 验证端口号
    const port = parseInt(process.env.PORT);
    if (isNaN(port) || port < 1 || port > 65535) {
        errors.push('PORT must be a valid port number (1-65535)');
    }
    
    // 验证域名
    if (!process.env.BASE_DOMAIN) {
        errors.push('BASE_DOMAIN is required');
    }
    
    // 验证数据库路径
    if (!process.env.DB_PATH) {
        errors.push('DB_PATH is required');
    }
    
    if (errors.length > 0) {
        console.error('Configuration validation errors:');
        errors.forEach(error => console.error(`  - ${error}`));
        throw new Error('Invalid configuration');
    }
}

// 主配置对象
const config = {
    // 服务器配置
    server: {
        port: parseInt(process.env.PORT) || 1314,
        env: process.env.NODE_ENV || 'production',
        corsOrigin: process.env.CORS_ORIGIN || '*',
        staticFilesCache: parseInt(process.env.STATIC_FILES_CACHE) || 3600
    },
    
    // 域名配置
    domain: {
        base: process.env.BASE_DOMAIN || 'love.yuh.cool',
        url: process.env.BASE_URL || 'https://love.yuh.cool'
    },
    
    // 数据库配置
    database: {
        path: process.env.DB_PATH || './data/love_messages.db',
        backupDir: process.env.DB_BACKUP_DIR || './data/backups'
    },
    
    // 日志配置
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        file: process.env.LOG_FILE || './logs/backend.log'
    },
    
    // 特殊日期配置
    dates: {
        yuBirthday: process.env.YU_BIRTHDAY || '01-16',
        wangBirthday: process.env.WANG_BIRTHDAY || '04-15',
        loveStartDate: process.env.LOVE_START_DATE || '2023-01-01'
    },
    
    // API配置
    api: {
        prefix: process.env.API_PREFIX || '/api',
        messagesEndpoint: process.env.MESSAGES_ENDPOINT || '/messages',
        healthEndpoint: process.env.HEALTH_ENDPOINT || '/health'
    }
};

// 配置验证（仅在非测试环境下执行）
if (process.env.NODE_ENV !== 'test') {
    try {
        validateConfig();
        console.log('✅ Configuration loaded successfully');
        console.log(`   Server: ${config.server.env} mode on port ${config.server.port}`);
        console.log(`   Domain: ${config.domain.url}`);
        console.log(`   Database: ${config.database.path}`);
    } catch (error) {
        console.error('❌ Configuration validation failed:', error.message);
        process.exit(1);
    }
}

// 导出配置对象
module.exports = config;
