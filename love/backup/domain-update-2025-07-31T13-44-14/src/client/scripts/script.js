

// Love counter functionality
const startDate = new Date('2023-04-23T00:00:00');

function updateLoveCounter() {
    const now = new Date();
    const timeDiff = now - startDate;
    
    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);
    
    document.getElementById('days').textContent = days;
    document.getElementById('hours').textContent = hours;
    document.getElementById('minutes').textContent = minutes;
    document.getElementById('seconds').textContent = seconds;
}

// Birthday countdown functionality
function updateBirthdayCountdown() {
    const now = new Date();
    const currentYear = now.getFullYear();
    
    // <PERSON>'s birthday: April 15
    let girlBirthday = new Date(currentYear, 3, 15); // Month is 0-indexed
    if (girlBirthday < now) {
        girlBirthday = new Date(currentYear + 1, 3, 15);
    }

    // Yu's birthday: January 16 (农历腊月初八)
    let boyBirthday = new Date(currentYear, 0, 16); // Month is 0-indexed
    if (boyBirthday < now) {
        boyBirthday = new Date(currentYear + 1, 0, 16);
    }
    
    const girlDaysLeft = Math.ceil((girlBirthday - now) / (1000 * 60 * 60 * 24));
    const boyDaysLeft = Math.ceil((boyBirthday - now) / (1000 * 60 * 60 * 24));
    
    document.getElementById('girl-countdown').textContent = girlDaysLeft;
    document.getElementById('boy-countdown').textContent = boyDaysLeft;
}

// Modern quotes functionality using static data
let lastQuoteIndex = -1; // 记录上一次的索引，避免重复

function loadInitialQuote() {
    const quoteElement = document.getElementById('quote-text');
    
    try {
        // Check if modernLoveQuotes is available
        if (typeof modernLoveQuotes !== 'undefined' && modernLoveQuotes.length > 0) {
            const randomIndex = Math.floor(Math.random() * modernLoveQuotes.length);
            lastQuoteIndex = randomIndex;
            const selectedQuote = modernLoveQuotes[randomIndex];
            
            quoteElement.textContent = selectedQuote.content;
        } else {
            // Fallback to default quote if data not available
            quoteElement.textContent = '爱情不是寻找一个完美的人，而是学会用完美的眼光欣赏一个不完美的人。';
        }
    } catch (error) {
        console.error('Error loading initial quote:', error);
        // Keep the default quote on error
        quoteElement.textContent = '爱情不是寻找一个完美的人，而是学会用完美的眼光欣赏一个不完美的人。';
    }
}

function changeQuote() {
    const quoteElement = document.getElementById('quote-text');
    
    // Add fade out effect
    quoteElement.style.opacity = '0';
    
    try {
        // Check if modernLoveQuotes is available
        if (typeof modernLoveQuotes !== 'undefined' && modernLoveQuotes.length > 0) {
            setTimeout(() => {
                let randomIndex;
                let attempts = 0;
                const maxAttempts = 10;
                
                // 确保选择的不是上一次的情话，最多尝试10次避免无限循环
                do {
                    randomIndex = Math.floor(Math.random() * modernLoveQuotes.length);
                    attempts++;
                } while (randomIndex === lastQuoteIndex && attempts < maxAttempts && modernLoveQuotes.length > 1);
                
                lastQuoteIndex = randomIndex;
                const selectedQuote = modernLoveQuotes[randomIndex];
                
                quoteElement.textContent = selectedQuote.content;
                quoteElement.style.opacity = '1';
            }, 300);
        } else if (typeof window.modernLoveQuotes !== 'undefined' && window.modernLoveQuotes.length > 0) {
            modernLoveQuotes = window.modernLoveQuotes;
            // 递归调用自己，现在应该能找到数据了
            changeQuote();
            return;
        } else {
            // Fallback quote if data not available
            setTimeout(() => {
                quoteElement.textContent = '爱情不是寻找一个完美的人，而是学会用完美的眼光欣赏一个不完美的人。';
                quoteElement.style.opacity = '1';
            }, 300);
        }
    } catch (error) {
        console.error('Error changing quote:', error);
        // Fallback quote on error
        setTimeout(() => {
            quoteElement.textContent = '爱情不是寻找一个完美的人，而是学会用完美的眼光欣赏一个不完美的人。';
            quoteElement.style.opacity = '1';
        }, 300);
    }
}

// Click effects
function createClickEffect(e) {
    const effect = document.createElement('div');
    effect.className = 'click-effect';
    effect.style.position = 'fixed';
    effect.style.left = e.clientX + 'px';
    effect.style.top = e.clientY + 'px';
    effect.style.width = '20px';
    effect.style.height = '20px';
    effect.style.background = '#ff6b9d';
    effect.style.borderRadius = '50%';
    effect.style.pointerEvents = 'none';
    effect.style.zIndex = '9999';
    effect.style.animation = 'clickPop 0.6s ease-out forwards';
    
    document.body.appendChild(effect);
    
    setTimeout(() => {
        document.body.removeChild(effect);
    }, 600);
}

// Add click effect CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes clickPop {
        0% {
            transform: scale(0);
            opacity: 1;
        }
        50% {
            transform: scale(1.5);
            opacity: 0.7;
        }
        100% {
            transform: scale(2);
            opacity: 0;
        }
    }
    
    .quote-text {
        transition: opacity 0.3s ease;
    }
`;
document.head.appendChild(style);

// Update placeholder text based on selected author
function updatePlaceholder() {
    const authorRadios = document.querySelectorAll('input[name="author"]');
    const messageTextElement = document.getElementById('messageText');

    if (!messageTextElement) return; // 如果元素不存在则返回

    let selectedAuthor = '';
    authorRadios.forEach(radio => {
        if (radio.checked) {
            selectedAuthor = radio.value;
        }
    });

    if (selectedAuthor) {
        if (selectedAuthor === 'Yu') {
            messageTextElement.placeholder = '在这里写下Yu想说的话...';
        } else if (selectedAuthor === 'Wang') {
            messageTextElement.placeholder = '在这里写下Wang想说的话...';
        } else {
            messageTextElement.placeholder = '在这里写下你想说的话...';
        }
    } else {
        messageTextElement.placeholder = '选择一个用户，然后写下你想说的话...';
    }
}

// Add event listeners to author radio buttons
function initializeAuthorSelection() {
    const authorRadios = document.querySelectorAll('input[name="author"]');
    authorRadios.forEach(radio => {
        radio.addEventListener('change', updatePlaceholder);
    });
    // Update placeholder on page load
    updatePlaceholder();
}

// Initialize everything when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Check if modernLoveQuotes is loaded and set global variable if needed
    if (typeof modernLoveQuotes === 'undefined' && typeof window.modernLoveQuotes !== 'undefined') {
        modernLoveQuotes = window.modernLoveQuotes;
    }
    
    // Update counters immediately and then every second
    updateLoveCounter();
    updateBirthdayCountdown();
    
    setInterval(updateLoveCounter, 1000);
    setInterval(updateBirthdayCountdown, 60000); // Update birthday countdown every minute
    
    // Load messages when page loads
    loadMessages();
    
    // Load initial quote from new API
    loadInitialQuote();
    
    // Add click effects to the page
    document.addEventListener('click', createClickEffect);
    
    // Add some interactive animations
    const cards = document.querySelectorAll('.counter-card, .birthday-card, .quote-card, .memory-item');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Add smooth scrolling for better UX
    document.documentElement.style.scrollBehavior = 'smooth';

    // Initialize author selection and placeholder updates
    initializeAuthorSelection();
});

// Add some random heart animations
function createRandomHeart() {
    const heart = document.createElement('div');
    heart.className = 'random-heart';
    heart.innerHTML = '💖';
    heart.style.position = 'fixed';
    heart.style.left = Math.random() * window.innerWidth + 'px';
    heart.style.top = window.innerHeight + 'px';
    heart.style.fontSize = (Math.random() * 20 + 15) + 'px';
    heart.style.pointerEvents = 'none';
    heart.style.zIndex = '1000';
    heart.style.animation = 'floatUp 4s linear forwards';
    
    document.body.appendChild(heart);
    
    setTimeout(() => {
        if (document.body.contains(heart)) {
            document.body.removeChild(heart);
        }
    }, 4000);
}

// Add floating heart animation CSS
const heartStyle = document.createElement('style');
heartStyle.textContent = `
    @keyframes floatUp {
        0% {
            transform: translateY(0) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: translateY(-100vh) rotate(360deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(heartStyle);

// Create random hearts occasionally
setInterval(createRandomHeart, 8000);

// Add special effects for special dates
function checkSpecialDates() {
    const now = new Date();
    const month = now.getMonth() + 1;
    const day = now.getDate();
    
    // Check if it's someone's birthday
    if ((month === 4 && day === 15) || (month === 2 && day === 28)) {
        // Add birthday celebration effect
        document.body.style.background = 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)';
        
        // Create more hearts for birthday
        setInterval(createRandomHeart, 2000);
    }
    
    // Check if it's anniversary (April 23)
    if (month === 4 && day === 23) {
        // Add anniversary celebration effect
        document.body.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)';
        
        // Show special anniversary message
        const specialMessage = document.createElement('div');
        specialMessage.innerHTML = '🎉 今天是我们的纪念日！🎉';
        specialMessage.style.position = 'fixed';
        specialMessage.style.top = '20px';
        specialMessage.style.left = '50%';
        specialMessage.style.transform = 'translateX(-50%)';
        specialMessage.style.background = 'rgba(255, 255, 255, 0.95)';
        specialMessage.style.padding = '15px 30px';
        specialMessage.style.borderRadius = '25px';
        specialMessage.style.fontSize = '1.2rem';
        specialMessage.style.fontWeight = 'bold';
        specialMessage.style.color = '#ff6b9d';
        specialMessage.style.zIndex = '10000';
        specialMessage.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';
        specialMessage.style.animation = 'bounce 2s infinite';
        
        document.body.appendChild(specialMessage);
    }
}

// Check for special dates on load
document.addEventListener('DOMContentLoaded', async () => {
    checkSpecialDates();
    await initializeApiConfig();
});

// Love Messages functionality
// 使用配置管理系统获取API URL，支持动态配置
let API_BASE_URL = 'https://love.yuh.cool/api'; // 默认值，将被配置系统覆盖

// 初始化API配置
async function initializeApiConfig() {
    try {
        if (window.loveConfig) {
            API_BASE_URL = await window.loveConfig.getApiBaseUrl();
            console.log('✅ API配置已更新:', API_BASE_URL);
        }
    } catch (error) {
        console.warn('⚠️ API配置初始化失败，使用默认配置:', error.message);
    }
}

// Helper function to escape HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Romantic message templates
const messageTemplates = [
    {
        category: "日常甜蜜",
        messages: [
            "今天也想你了，就像昨天一样，就像明天一样。",
            "你是我见过的最美的风景，也是我最想要的未来。",
            "和你在一起的每一天，都是我人生中最美好的一天。",
            "你的笑容是我每天最期待的阳光。",
            "想把世界上最好的都给你，包括我自己。"
        ]
    },
    {
        category: "思念表达",
        messages: [
            "距离分不开我们，时间冲不淡我们，我想你。",
            "每一个想你的夜晚，星星都为我作证。",
            "想你的时候，连呼吸都带着甜蜜的味道。",
            "不管多远，我的心都在你身边。",
            "想你是我每天必做的功课，爱你是我一生不变的承诺。"
        ]
    },
    {
        category: "感谢有你",
        messages: [
            "谢谢你出现在我的生命里，让我的世界变得如此美好。",
            "感谢命运让我遇见你，感谢时光让我爱上你。",
            "有你的日子，连空气都是甜的。",
            "你是我生命中最美的意外，也是最珍贵的礼物。",
            "因为有你，我才知道什么是真正的幸福。"
        ]
    },
    {
        category: "未来憧憬",
        messages: [
            "我想和你一起看遍世界的美景，走过人生的四季。",
            "愿我们的爱情像美酒一样，越陈越香。",
            "想和你一起变老，一起看夕阳西下。",
            "我们的故事才刚刚开始，最美的章节还在后面。",
            "想和你一起创造属于我们的小世界。"
        ]
    },
    {
        category: "浪漫告白",
        messages: [
            "你是我心中的唯一，也是我生命的全部意义。",
            "如果爱你是错，我愿意错一辈子。",
            "你是我的太阳，照亮了我整个世界。",
            "爱你不需要理由，就像鸟儿爱天空，鱼儿爱大海。",
            "你是我见过的最美的诗，也是我想写的最动人的歌。"
        ]
    },
    {
        category: "节日祝福",
        messages: [
            "每一个节日都想和你一起度过，每一个祝福都想和你分享。",
            "节日快乐，我的爱人，愿我们的爱情永远像今天一样甜蜜。",
            "在这个特别的日子里，我想对你说：我爱你，永远爱你。",
            "愿我们的每一个节日都充满爱意和温馨。",
            "节日的意义因为有你而变得更加美好。"
        ]
    }
];

// Load messages from server
async function loadMessages() {
    try {
        console.log('Loading messages from:', `${API_BASE_URL}/messages`);
        const response = await fetch(`${API_BASE_URL}/messages`);
        console.log('Load response status:', response.status);

        // Check if response is ok
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Check content type
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            const text = await response.text();
            console.error('Non-JSON response:', text.substring(0, 200));
            throw new Error('服务器返回了非JSON响应');
        }

        const result = await response.json();
        console.log('Load response data:', result);

        if (result.success) {
            console.log('Messages loaded successfully:', result.data?.length || 0, 'messages');
            return result.data || [];
        } else {
            console.error('Failed to load messages:', result.message);
            return [];
        }
    } catch (error) {
        console.error('Error loading messages:', error);
        // Show user-friendly error message
        const messagesList = document.getElementById('messagesList');
        if (messagesList) {
            messagesList.innerHTML = `
                <div class="error-message" style="
                    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
                    color: white;
                    padding: 20px;
                    border-radius: 15px;
                    text-align: center;
                    margin: 20px 0;
                    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
                ">
                    <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i>
                    <h3 style="margin: 10px 0;">加载留言失败</h3>
                    <p style="margin: 5px 0;">请检查网络连接或稍后重试</p>
                    <button onclick="displayMessages()" style="
                        background: rgba(255,255,255,0.2);
                        border: 1px solid rgba(255,255,255,0.3);
                        color: white;
                        padding: 8px 16px;
                        border-radius: 20px;
                        cursor: pointer;
                        margin-top: 10px;
                    ">重新加载</button>
                </div>
            `;
        }
        return [];
    }
}

// Save new message to server
async function saveNewMessage(message) {
    try {
        console.log('Saving message:', message);
        console.log('API URL:', `${API_BASE_URL}/messages`);

        const response = await fetch(`${API_BASE_URL}/messages`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                author: message.author,
                content: message.content
            })
        });

        console.log('Response status:', response.status);

        // Check if response is ok
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Check content type
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            const text = await response.text();
            console.error('Non-JSON response:', text.substring(0, 200));
            throw new Error('服务器返回了非JSON响应');
        }

        const result = await response.json();
        console.log('Response data:', result);

        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message || '保存留言失败');
        }
    } catch (error) {
        console.error('Error saving message:', error);
        // Provide more specific error messages
        if (error.message.includes('Failed to fetch')) {
            throw new Error('网络连接失败，请检查网络连接');
        } else if (error.message.includes('Non-JSON response')) {
            throw new Error('服务器响应格式错误，请稍后重试');
        } else {
            throw error;
        }
    }
}

// Format message content with proper indentation
function formatMessageContent(content) {
    // 清理开头和结尾的空白
    const trimmedContent = content.trim();

    console.log('formatMessageContent input:', JSON.stringify(trimmedContent));

    // 检查是否包含手动换行
    const lines = trimmedContent.split('\n');

    if (lines.length === 1) {
        // 单行内容，HTML转义后直接返回，由CSS的text-indent处理首行缩进
        const escapedContent = trimmedContent.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
        console.log('single line, formatted content:', escapedContent);
        return escapedContent;
    }

    // 多行内容，需要将每一行都包装成独立的段落，这样每行都能应用text-indent
    const formattedLines = lines.map((line, index) => {
        // HTML转义
        const escapedLine = line.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');

        // 每行都包装成div，这样每行都会应用CSS的text-indent
        return `<div class="message-line">${escapedLine}</div>`;
    });

    const result = formattedLines.join('');
    console.log('multi-line formatted content:', result);
    return result;
}

// Display messages with grouping and collapsing
async function displayMessages() {
    const messages = await loadMessages();
    const messagesList = document.getElementById('messagesList');
    const emptyState = document.getElementById('emptyState');

    if (messages.length === 0) {
        messagesList.style.display = 'none';
        emptyState.style.display = 'block';
        return;
    }

    messagesList.style.display = 'block';
    emptyState.style.display = 'none';

    // Sort messages by timestamp (newest first)
    messages.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Group messages by time period
    const groupedMessages = groupMessagesByTime(messages);

    // Generate HTML for grouped messages
    messagesList.innerHTML = Object.entries(groupedMessages).map(([groupKey, group]) => {
        const isCollapsed = shouldGroupBeCollapsed(groupKey, group, groupedMessages);

        // 生成内容HTML - 如果是年份分组且有月份子分组，则生成月份分组；否则直接生成消息
        let contentHtml;
        if (group.hasMonthGroups) {
            // 年份分组有月份子分组
            contentHtml = Object.entries(group.monthGroups).map(([monthKey, monthGroup]) => {
                const monthIsCollapsed = shouldMonthGroupBeCollapsed(monthKey, monthGroup, group.monthGroups);
                const monthMessagesHtml = monthGroup.messages.map(message => `
                    <div class="message-card ${getMessageClass(message.author)}" data-id="${message.id}">
                        <div class="message-header">
                            <div class="message-author">
                                <i class="fas fa-heart"></i>
                                <span class="author-name ${getAuthorClass(message.author)}">${message.author}</span>
                            </div>
                            <div class="message-date">${formatDate(message.timestamp)}</div>
                            <div class="message-actions">
                                <button class="edit-btn" onclick="editMessage(${message.id}, \`${message.content.replace(/`/g, '\\`').replace(/\$/g, '\\$')}\`, '${message.author}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="delete-btn" onclick="deleteMessage(${message.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="message-content">${formatMessageContent(message.content)}</div>
                    </div>
                `).join('');

                const monthMessageIds = monthGroup.messages.map(m => m.id).join(',');

                return `
                    <div class="month-group" data-month-group="${monthKey}">
                        <div class="month-header ${monthIsCollapsed ? 'collapsed' : ''}">
                            <div class="month-title" onclick="toggleMonthGroup('${monthKey}')">
                                <i class="fas fa-chevron-${monthIsCollapsed ? 'right' : 'down'}"></i>
                                <div class="month-title-content">
                                    <span class="month-main-title">${monthGroup.title}</span>
                                </div>
                                <span class="month-heart">💝</span>
                                <span class="month-message-count">(${monthGroup.messages.length}条)</span>
                            </div>
                            <div class="month-actions">
                                <button class="delete-month-btn" onclick="event.stopPropagation(); deleteGroupMessages('${monthKey}', [${monthMessageIds}])" title="删除这个月的所有留言">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="month-content ${monthIsCollapsed ? 'collapsed' : ''}">
                            ${monthMessagesHtml}
                        </div>
                    </div>
                `;
            }).join('');
        } else {
            // 普通分组，直接显示消息
            contentHtml = group.messages.map(message => `
                <div class="message-card ${getMessageClass(message.author)}" data-id="${message.id}">
                    <div class="message-header">
                        <div class="message-author">
                            <i class="fas fa-heart"></i>
                            <span class="author-name ${getAuthorClass(message.author)}">${message.author}</span>
                        </div>
                        <div class="message-date">${formatDate(message.timestamp)}</div>
                        <div class="message-actions">
                            <button class="edit-btn" onclick="editMessage(${message.id}, \`${message.content.replace(/`/g, '\\`').replace(/\$/g, '\\$')}\`, '${message.author}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="delete-btn" onclick="deleteMessage(${message.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="message-content">${formatMessageContent(message.content)}</div>
                </div>
            `).join('');
        }

        const messageIds = group.messages.map(m => m.id).join(',');

        return `
            <div class="message-group" data-group="${groupKey}">
                <div class="group-header ${isCollapsed ? 'collapsed' : ''}">
                    <div class="group-title" onclick="toggleGroup('${groupKey}')">
                        <i class="fas fa-chevron-${isCollapsed ? 'right' : 'down'}"></i>
                        <div class="title-content">
                            <span class="main-title">${group.title}</span>
                            <span class="sub-title">${group.subtitle}</span>
                        </div>
                        <span class="title-heart">💖</span>
                        <span class="message-count">(${group.messages.length}条)</span>
                    </div>
                    <div class="group-actions">
                        <button class="delete-group-btn" onclick="event.stopPropagation(); deleteGroupMessages('${groupKey}', [${messageIds}])" title="删除这一天的所有留言">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="group-content ${isCollapsed ? 'collapsed' : ''}">
                    ${contentHtml}
                </div>
            </div>
        `;
    }).join('');
}

// Group messages by time period (使用北京时间)
function groupMessagesByTime(messages) {
    const groups = {};
    const now = new Date();

    messages.forEach(message => {
        const beijingDate = getBeijingDate(message.timestamp);
        const nowBeijing = getBeijingDate(now.getTime());

        // 获取日期字符串进行比较（YYYY-MM-DD格式）
        const messageDateStr = getBeijingDateString(message.timestamp);
        const todayDateStr = getBeijingDateString(now.getTime());

        // 计算日期差
        const diffDays = calculateDaysDifference(message.timestamp, now.getTime());

        console.log(`Message: ${message.content.substring(0, 20)}...`);
        console.log(`Message timestamp: ${message.timestamp}`);
        console.log(`Message Beijing date: ${beijingDate.toISOString()}`);
        console.log(`Message date: ${messageDateStr}, Today: ${todayDateStr}, Diff: ${diffDays} days`);

        let groupKey, groupTitle, groupSubtitle;

        const currentYear = nowBeijing.getFullYear();
        const currentMonth = nowBeijing.getMonth();
        const messageYear = beijingDate.getFullYear();
        const messageMonth = beijingDate.getMonth();

        if (diffDays === 0) {
            groupKey = 'today';
            groupTitle = '今天';
            groupSubtitle = formatGroupDate(nowBeijing);
            console.log(`→ Grouped as TODAY: ${groupKey}`);
        } else if (diffDays === 1) {
            groupKey = 'yesterday';
            groupTitle = '昨天';
            // 昨天的日期 = 今天 - 1天
            const yesterdayDate = new Date(nowBeijing.getTime() - 24 * 60 * 60 * 1000);
            groupSubtitle = formatGroupDate(yesterdayDate);
            console.log(`→ Grouped as YESTERDAY: ${groupKey}`);
        } else if (diffDays === 2) {
            groupKey = 'daybeforeyesterday';
            groupTitle = '前天';
            // 前天的日期 = 今天 - 2天
            const dayBeforeYesterdayDate = new Date(nowBeijing.getTime() - 2 * 24 * 60 * 60 * 1000);
            groupSubtitle = formatGroupDate(dayBeforeYesterdayDate);
            console.log(`→ Grouped as DAY BEFORE YESTERDAY: ${groupKey}`);
        } else if (diffDays <= 7) {
            // 近期（本周）
            groupKey = 'thisweek';
            groupTitle = '近期';
            groupSubtitle = '本周';
        } else if (currentYear === messageYear && currentMonth === messageMonth) {
            // 本月
            groupKey = 'thismonth';
            groupTitle = '本月';
            groupSubtitle = formatGroupYearMonth(beijingDate);
        } else if (currentYear === messageYear && currentMonth - messageMonth === 1) {
            // 上个月
            groupKey = 'lastmonth';
            groupTitle = '上个月';
            groupSubtitle = formatGroupYearMonth(beijingDate);
        } else {
            // 更早时间按年分组
            groupKey = `year-${messageYear}`;
            groupTitle = formatGroupYearSmart(beijingDate, currentYear);
            groupSubtitle = '';
        }

        if (!groups[groupKey]) {
            groups[groupKey] = {
                title: groupTitle,
                subtitle: groupSubtitle,
                messages: [],
                oldestDate: beijingDate,
                newestDate: beijingDate
            };
        }

        groups[groupKey].messages.push(message);
        if (beijingDate < groups[groupKey].oldestDate) {
            groups[groupKey].oldestDate = beijingDate;
        }
        if (beijingDate > groups[groupKey].newestDate) {
            groups[groupKey].newestDate = beijingDate;
        }
    });

    // Sort messages within each group by timestamp (newest first)
    Object.values(groups).forEach(group => {
        group.messages.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    });

    // 为年份分组添加月份子分组
    Object.keys(groups).forEach(groupKey => {
        if (groupKey.startsWith('year-')) {
            const yearGroup = groups[groupKey];
            const monthSubGroups = groupMessagesByMonth(yearGroup.messages);

            // 如果该年份有多个月份的留言，则创建月份子分组
            if (Object.keys(monthSubGroups).length > 1) {
                yearGroup.monthGroups = monthSubGroups;
                yearGroup.hasMonthGroups = true;
            } else {
                yearGroup.hasMonthGroups = false;
            }
        }
    });

    return groups;
}

// Group messages by month within a year (用于年份内的月份分组)
function groupMessagesByMonth(messages) {
    const monthGroups = {};

    messages.forEach(message => {
        const beijingDate = getBeijingDate(message.timestamp);
        const year = beijingDate.getFullYear();
        const month = beijingDate.getMonth(); // 0-11

        const monthKey = `${year}-${month}`;
        const monthTitle = formatGroupYearMonth(beijingDate);

        if (!monthGroups[monthKey]) {
            monthGroups[monthKey] = {
                title: monthTitle,
                subtitle: '',
                messages: [],
                oldestDate: beijingDate,
                newestDate: beijingDate,
                year: year,
                month: month
            };
        }

        monthGroups[monthKey].messages.push(message);
        if (beijingDate < monthGroups[monthKey].oldestDate) {
            monthGroups[monthKey].oldestDate = beijingDate;
        }
        if (beijingDate > monthGroups[monthKey].newestDate) {
            monthGroups[monthKey].newestDate = beijingDate;
        }
    });

    // Sort messages within each month group by timestamp (newest first)
    Object.values(monthGroups).forEach(monthGroup => {
        monthGroup.messages.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    });

    return monthGroups;
}

// Format date for group headers (使用北京时间)
function formatGroupDate(beijingDate) {
    return beijingDate.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        timeZone: 'Asia/Shanghai'
    }).replace(/\//g, '.');
}

function formatGroupDateRange(startDate, messages) {
    if (messages.length <= 1) return formatGroupDate(startDate);

    const dates = messages.map(m => getBeijingDate(m.timestamp)).sort((a, b) => a - b);
    const oldest = dates[0];
    const newest = dates[dates.length - 1];

    return `${formatGroupDate(oldest)} - ${formatGroupDate(newest)}`;
}

function formatGroupMonth(beijingDate) {
    return beijingDate.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        timeZone: 'Asia/Shanghai'
    });
}

function formatGroupYear(beijingDate) {
    return beijingDate.getFullYear() + '年';
}

function formatGroupYearMonth(beijingDate) {
    const year = beijingDate.getFullYear();
    const month = beijingDate.getMonth() + 1;
    return `${year}年${month}月`;
}

function formatGroupYearSmart(beijingDate, currentYear) {
    const year = beijingDate.getFullYear();
    if (year === currentYear) {
        return '今年';
    } else {
        return year + '年';
    }
}

// Determine if a group should be collapsed by default
function shouldGroupBeCollapsed(groupKey, group, allGroups) {
    // Find the most recent group (with the newest date)
    const mostRecentGroup = Object.values(allGroups).reduce((latest, current) => {
        return current.newestDate > latest.newestDate ? current : latest;
    });

    // Only expand the most recent group
    return group !== mostRecentGroup;
}

// Toggle group collapse/expand
function toggleGroup(groupKey) {
    console.log('toggleGroup called with:', groupKey); // 调试日志

    const group = document.querySelector(`[data-group="${groupKey}"]`);
    if (!group) {
        console.error('Group not found:', groupKey);
        return;
    }

    const header = group.querySelector('.group-header');
    const content = group.querySelector('.group-content');
    const icon = header.querySelector('i');

    if (!header || !content || !icon) {
        console.error('Group elements not found:', { header, content, icon });
        return;
    }

    const isCollapsed = content.classList.contains('collapsed');
    console.log('Current collapsed state:', isCollapsed);

    if (isCollapsed) {
        content.classList.remove('collapsed');
        header.classList.remove('collapsed');
        icon.className = 'fas fa-chevron-down';
        console.log('Expanded group:', groupKey);
    } else {
        content.classList.add('collapsed');
        header.classList.add('collapsed');
        icon.className = 'fas fa-chevron-right';
        console.log('Collapsed group:', groupKey);
    }
}

// Determine if a month group should be collapsed by default
function shouldMonthGroupBeCollapsed(monthKey, monthGroup, allMonthGroups) {
    // Find the most recent month group (with the newest date)
    const mostRecentMonthGroup = Object.values(allMonthGroups).reduce((latest, current) => {
        return current.newestDate > latest.newestDate ? current : latest;
    });

    // Only expand the most recent month group
    return monthGroup !== mostRecentMonthGroup;
}

// Toggle month group collapse/expand
function toggleMonthGroup(monthKey) {
    console.log('toggleMonthGroup called with:', monthKey); // 调试日志

    const monthGroup = document.querySelector(`[data-month-group="${monthKey}"]`);
    if (!monthGroup) {
        console.error('Month group not found:', monthKey);
        return;
    }

    const header = monthGroup.querySelector('.month-header');
    const content = monthGroup.querySelector('.month-content');
    const icon = header.querySelector('i');

    if (!header || !content || !icon) {
        console.error('Month group elements not found:', { header, content, icon });
        return;
    }

    const isCollapsed = content.classList.contains('collapsed');
    console.log('Current month collapsed state:', isCollapsed);

    if (isCollapsed) {
        content.classList.remove('collapsed');
        header.classList.remove('collapsed');
        icon.className = 'fas fa-chevron-down';
        console.log('Expanded month group:', monthKey);
    } else {
        content.classList.add('collapsed');
        header.classList.add('collapsed');
        icon.className = 'fas fa-chevron-right';
        console.log('Collapsed month group:', monthKey);
    }
}

// Show romantic confirmation dialog
function showRomanticConfirm(message, onConfirm) {
    const modal = document.createElement('div');
    modal.className = 'romantic-confirm-modal show';
    modal.innerHTML = `
        <div class="romantic-confirm-overlay"></div>
        <div class="romantic-confirm-content">
            <div class="romantic-confirm-header">
                <div class="floating-hearts">
                    <span>💖</span>
                    <span>💕</span>
                    <span>💝</span>
                    <span>💗</span>
                </div>
                <h3>温柔提醒</h3>
            </div>
            <div class="romantic-confirm-body">
                <p>${message}</p>
            </div>
            <div class="romantic-confirm-actions">
                <button class="romantic-btn romantic-btn-cancel">
                    <i class="fas fa-heart-broken"></i>
                    取消
                </button>
                <button class="romantic-btn romantic-btn-confirm">
                    <i class="fas fa-heart"></i>
                    确定
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Add event listeners
    modal.querySelector('.romantic-btn-cancel').onclick = () => {
        document.body.removeChild(modal);
    };

    modal.querySelector('.romantic-btn-confirm').onclick = () => {
        document.body.removeChild(modal);
        onConfirm();
    };

    modal.querySelector('.romantic-confirm-overlay').onclick = () => {
        document.body.removeChild(modal);
    };
}

// Delete all messages in a group
async function deleteGroupMessages(groupKey, messageIds) {
    const group = document.querySelector(`[data-group="${groupKey}"]`);
    const groupTitle = group.querySelector('.main-title').textContent;
    const groupDate = group.querySelector('.sub-title').textContent;

    showRomanticConfirm(
        `亲爱的，确定要删除 "${groupTitle} ${groupDate}" 的所有 ${messageIds.length} 条珍贵留言吗？<br><br>这些美好的回忆一旦删除就无法找回了哦~ 💔`,
        async () => {
            try {
                // Delete all messages in the group
                for (const messageId of messageIds) {
                    const response = await fetch(`${API_BASE_URL}/messages/${messageId}`, {
                        method: 'DELETE'
                    });

                    if (!response.ok) {
                        throw new Error(`删除留言 ${messageId} 失败`);
                    }
                }

                // Refresh the display
                await displayMessages();
                showSuccessMessage(`已删除 "${groupTitle}" 的所有留言`);

            } catch (error) {
                console.error('Error deleting group messages:', error);
                alert('删除失败：' + error.message);
            }
        }
    );
}

// Create confetti effect
function createConfetti() {
    const confettiContainer = document.createElement('div');
    confettiContainer.className = 'confetti-container';
    document.body.appendChild(confettiContainer);

    const colors = ['#ff6b9d', '#c44569', '#f8b500', '#667eea', '#764ba2', '#ff9a9e', '#fecfef'];
    const shapes = ['💖', '💕', '💝', '💗', '🌸', '🌺', '✨', '⭐', '🎉', '🎊'];

    // Create multiple bursts
    for (let burst = 0; burst < 3; burst++) {
        setTimeout(() => {
            for (let i = 0; i < 50; i++) {
                const confetti = document.createElement('div');
                confetti.className = 'confetti-piece';

                // Random shape or colored square
                if (Math.random() > 0.5) {
                    confetti.textContent = shapes[Math.floor(Math.random() * shapes.length)];
                    confetti.style.fontSize = Math.random() * 20 + 15 + 'px';
                } else {
                    confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                    confetti.style.width = Math.random() * 10 + 5 + 'px';
                    confetti.style.height = confetti.style.width;
                }

                // Random starting position
                confetti.style.left = Math.random() * window.innerWidth + 'px';
                confetti.style.top = '-20px';

                // Random animation duration and delay
                const duration = Math.random() * 3 + 2;
                const delay = Math.random() * 0.5;

                confetti.style.animation = `confettiFall ${duration}s ${delay}s ease-out forwards`;

                confettiContainer.appendChild(confetti);
            }
        }, burst * 200);
    }

    // Remove confetti after animation
    setTimeout(() => {
        if (confettiContainer.parentNode) {
            document.body.removeChild(confettiContainer);
        }
    }, 6000);
}

// 获取北京时间的日期对象
function getBeijingDate(timestamp) {
    const date = new Date(timestamp);
    // 获取北京时间的偏移量（+8小时）
    const beijingOffset = 8 * 60; // 8小时 = 480分钟
    const utc = date.getTime() + (date.getTimezoneOffset() * 60000);
    const beijingTime = new Date(utc + (beijingOffset * 60000));
    return beijingTime;
}

// 获取北京时间的日期字符串 (YYYY-MM-DD)
function getBeijingDateString(timestamp) {
    const beijingDate = getBeijingDate(timestamp);
    // 不能用 toISOString()，因为它会转回UTC时间
    // 直接从北京时间对象获取年月日
    const year = beijingDate.getFullYear();
    const month = String(beijingDate.getMonth() + 1).padStart(2, '0');
    const day = String(beijingDate.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// 计算两个日期之间的天数差（基于北京时间的日期，不考虑具体时间）
function calculateDaysDifference(timestamp1, timestamp2) {
    const date1 = getBeijingDateString(timestamp1);
    const date2 = getBeijingDateString(timestamp2);

    const d1 = new Date(date1);
    const d2 = new Date(date2);

    // 计算天数差，timestamp2 - timestamp1，正数表示timestamp2更晚
    const diffTime = d2 - d1;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
}

// Format date for display (使用北京时间)
function formatDate(timestamp) {
    // 直接使用时区转换，确保显示北京时间
    const date = new Date(timestamp);
    const beijingDate = new Date(date.toLocaleString("en-US", {timeZone: "Asia/Shanghai"}));
    const now = new Date();
    const beijingNow = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Shanghai"}));

    // 计算日期差（基于北京时间的日期部分）
    const diffDays = calculateDaysDifference(timestamp, now.getTime());

    // 格式化时间显示 - 使用北京时间
    const timeString = date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'Asia/Shanghai'
    });

    if (diffDays === 0) {
        return '今天 ' + timeString;
    } else if (diffDays === 1) {
        return '昨天 ' + timeString;
    } else if (diffDays === 2) {
        return '前天 ' + timeString;
    } else {
        return date.toLocaleDateString('zh-CN', { timeZone: 'Asia/Shanghai' }) + ' ' + timeString;
    }
}

// Save new message or update existing message
async function saveMessage() {
    const authorRadios = document.querySelectorAll('input[name="author"]');
    const messageTextElement = document.getElementById('messageText');
    const messageText = messageTextElement.value.trim();
    const editingId = messageTextElement.dataset.editingId;

    let selectedAuthor = '';
    authorRadios.forEach(radio => {
        if (radio.checked) {
            selectedAuthor = radio.value;
        }
    });

    if (!selectedAuthor) {
        alert('请选择留言作者！');
        return;
    }

    if (!messageText) {
        alert('请输入留言内容！');
        return;
    }

    try {
        // Show loading state
        const saveBtn = document.querySelector('.save-btn');
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
        saveBtn.disabled = true;

        if (editingId) {
            // Update existing message
            const response = await fetch(`${API_BASE_URL}/messages/${editingId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content: messageText
                })
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || '更新留言失败');
            }

            createConfetti();
            showSuccessMessage('留言更新成功！💕');
        } else {
            // Create new message
            const newMessage = {
                author: selectedAuthor,
                content: messageText
            };

            await saveNewMessage(newMessage);
            createConfetti();
            showSuccessMessage('留言保存成功！💕');
        }

        // Clear form
        messageTextElement.value = '';
        delete messageTextElement.dataset.editingId;

        // Reset author selection
        authorRadios.forEach(radio => {
            radio.checked = false;
        });

        // Update placeholder after resetting
        updatePlaceholder();

        // Refresh display
        await displayMessages();

        // Restore button state
        saveBtn.innerHTML = '<i class="fas fa-heart"></i> 保存爱的留言';
        saveBtn.disabled = false;

    } catch (error) {
        alert('保存留言失败：' + error.message);

        // Restore button state
        const saveBtn = document.querySelector('.save-btn');
        saveBtn.innerHTML = '<i class="fas fa-heart"></i> 保存爱的留言';
        saveBtn.disabled = false;
    }
}

// Clear message form
function clearMessage() {
    document.getElementById('messageText').value = '';

    // Reset author selection
    const authorRadios = document.querySelectorAll('input[name="author"]');
    authorRadios.forEach(radio => {
        radio.checked = false;
    });

    // Update placeholder after clearing
    updatePlaceholder();
}

// Show success message
function showSuccessMessage(text) {
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.innerHTML = `
        <i class="fas fa-heart"></i>
        <span>${text}</span>
    `;

    document.body.appendChild(successDiv);

    setTimeout(() => {
        successDiv.classList.add('show');
    }, 100);

    setTimeout(() => {
        successDiv.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(successDiv);
        }, 300);
    }, 2000);
}

// Show templates modal
function showTemplates() {
    const modal = document.getElementById('templatesModal');
    const templatesGrid = document.getElementById('templatesGrid');

    // Generate templates HTML
    templatesGrid.innerHTML = messageTemplates.map(category => `
        <div class="template-category">
            <h4>${category.category}</h4>
            <div class="template-messages">
                ${category.messages.map(message => `
                    <div class="template-item" onclick="selectTemplate('${message.replace(/'/g, "\\'")}')">
                        <i class="fas fa-quote-left"></i>
                        <p>${message}</p>
                    </div>
                `).join('')}
            </div>
        </div>
    `).join('');

    modal.style.display = 'flex';
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

// Close templates modal
function closeTemplates() {
    const modal = document.getElementById('templatesModal');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

// Select template
function selectTemplate(template) {
    document.getElementById('messageText').value = template;
    closeTemplates();
}

// Edit message
function editMessage(messageId, content, author) {
    // Fill form with message data
    document.getElementById('messageText').value = content;

    // Select author
    const authorRadios = document.querySelectorAll('input[name="author"]');
    authorRadios.forEach(radio => {
        radio.checked = radio.value === author;
    });

    // Update placeholder after selecting author
    updatePlaceholder();

    // Store the message ID for updating
    document.getElementById('messageText').dataset.editingId = messageId;

    // Change save button text
    const saveBtn = document.querySelector('.save-btn');
    saveBtn.innerHTML = '<i class="fas fa-edit"></i> 更新留言';

    // Scroll to form
    document.querySelector('.message-input-card').scrollIntoView({ behavior: 'smooth' });
}

// Delete message
async function deleteMessage(messageId) {
    showRomanticConfirm(
        '亲爱的，确定要删除这条珍贵的留言吗？<br><br>这些美好的文字一旦删除就无法找回了哦~ 💔',
        async () => {
            try {
                const response = await fetch(`${API_BASE_URL}/messages/${messageId}`, {
                    method: 'DELETE'
                });

            const result = await response.json();

            if (result.success) {
                await displayMessages();
                showSuccessMessage('留言已删除');
            } else {
                alert('删除失败：' + result.message);
            }
            } catch (error) {
                console.error('Error deleting message:', error);
                alert('删除失败：' + error.message);
            }
        }
    );
}

// Export messages
async function exportMessages() {
    const messages = await loadMessages();

    if (messages.length === 0) {
        alert('没有留言可以导出！');
        return;
    }

    // Create export content
    let exportContent = '我们的爱情日志\n';
    exportContent += '==================\n\n';

    // Sort messages by timestamp (oldest first for export)
    messages.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    messages.forEach((message, index) => {
        const date = new Date(message.timestamp);
        exportContent += `${index + 1}. ${message.author} - ${date.toLocaleDateString('zh-CN')} ${date.toLocaleTimeString('zh-CN')}\n`;
        exportContent += `${message.content}\n\n`;
    });

    exportContent += '导出时间: ' + new Date().toLocaleString('zh-CN');

    // Create and download file
    const blob = new Blob([exportContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `爱情日志_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showSuccessMessage('导出成功！💕');
}

// Clear all messages
async function clearAllMessages() {
    if (confirm('确定要清空所有留言吗？此操作不可恢复！')) {
        try {
            const messages = await loadMessages();

            // Delete all messages one by one
            for (const message of messages) {
                await fetch(`${API_BASE_URL}/messages/${message.id}`, {
                    method: 'DELETE'
                });
            }

            await displayMessages();
            showSuccessMessage('所有留言已清空');
        } catch (error) {
            console.error('Error clearing messages:', error);
            alert('清空失败：' + error.message);
        }
    }
}

// Initialize messages on page load
document.addEventListener('DOMContentLoaded', function() {
    // ... existing DOMContentLoaded code ...

    // Initialize messages display
    displayMessages();

    // Close modal when clicking outside
    document.getElementById('templatesModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeTemplates();
        }
    });
});

// Helper function to get message class based on author
function getMessageClass(author) {
    switch(author) {
        case 'Yu':
            return 'yu-message';
        case 'Wang':
            return 'wang-message';
        case 'Other':
            return 'other-message';
        default:
            return 'other-message';
    }
}

// Helper function to get author name class for styling
function getAuthorClass(author) {
    switch(author) {
        case 'Yu':
            return 'author-yu';
        case 'Wang':
            return 'author-wang';
        case 'Other':
            return 'author-other';
        default:
            return 'author-other';
    }
}

// Memory navigation functionality - now redirects to separate pages
// The memory cards now link directly to individual HTML pages

// Messages collapse functionality
let isMessagesCollapsed = false;

function toggleMessagesCollapse() {
    const content = document.getElementById('messagesCollapsibleContent');
    const icon = document.getElementById('collapseIcon');
    const button = document.querySelector('.collapse-btn');

    isMessagesCollapsed = !isMessagesCollapsed;

    if (isMessagesCollapsed) {
        content.classList.add('collapsed');

        // Update collapse button - 折叠状态：旋转180度变成向上箭头，提示"展开"
        if (button) {
            button.classList.add('collapsed');
            button.title = '展开';
        }
    } else {
        content.classList.remove('collapsed');

        // Update collapse button - 展开状态：不旋转保持向下箭头，提示"折叠"
        if (button) {
            button.classList.remove('collapsed');
            button.title = '折叠';
        }
    }
}

// 移动端优化：添加触摸事件支持
function initMobileOptimizations() {
    // 检测是否为移动设备
    const isMobile = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile) {
        // 为所有可点击元素添加触摸反馈
        const clickableElements = document.querySelectorAll('.collapse-btn, .action-btn, .group-title, .delete-group-btn, .edit-btn, .delete-btn');

        clickableElements.forEach(element => {
            // 添加触摸开始事件
            element.addEventListener('touchstart', function(e) {
                this.style.transform = 'scale(0.95)';
                this.style.transition = 'transform 0.1s ease';
            }, { passive: true });

            // 添加触摸结束事件
            element.addEventListener('touchend', function(e) {
                setTimeout(() => {
                    this.style.transform = '';
                    this.style.transition = '';
                }, 100);
            }, { passive: true });

            // 添加触摸取消事件
            element.addEventListener('touchcancel', function(e) {
                this.style.transform = '';
                this.style.transition = '';
            }, { passive: true });
        });

        // 优化折叠按钮的点击区域
        const collapseBtn = document.querySelector('.collapse-btn');
        if (collapseBtn) {
            collapseBtn.style.minHeight = '44px';
            collapseBtn.style.minWidth = '44px';
        }

        // 为分组标题添加更好的触摸支持
        document.addEventListener('click', function(e) {
            const groupTitle = e.target.closest('.group-title');
            if (groupTitle) {
                e.preventDefault();
                e.stopPropagation();

                // 获取分组键
                const groupElement = groupTitle.closest('.message-group');
                if (groupElement) {
                    const groupKey = groupElement.getAttribute('data-group');
                    if (groupKey) {
                        toggleGroup(groupKey);
                    }
                }
            }
        });

        // 添加触摸事件支持
        document.addEventListener('touchend', function(e) {
            const groupTitle = e.target.closest('.group-title');
            if (groupTitle) {
                e.preventDefault();
                e.stopPropagation();

                // 获取分组键
                const groupElement = groupTitle.closest('.message-group');
                if (groupElement) {
                    const groupKey = groupElement.getAttribute('data-group');
                    if (groupKey) {
                        toggleGroup(groupKey);
                    }
                }
            }
        }, { passive: false });
    }
}

// 页面加载完成后初始化移动端优化
document.addEventListener('DOMContentLoaded', function() {
    initMobileOptimizations();

    // 监听窗口大小变化，重新初始化移动端优化
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            initMobileOptimizations();
        }, 250);
    });
});

// 动态星空生成器
function createDynamicStarfield() {
    const starfield = document.getElementById('dynamicStarfield');
    if (!starfield) return;

    const starCount = 50; // 星星数量
    const animations = ['starTwinkle1', 'starTwinkle2', 'starTwinkle3', 'starTwinkle4', 'starTwinkle5'];

    // 清空现有星星
    starfield.innerHTML = '';

    for (let i = 0; i < starCount; i++) {
        const star = document.createElement('div');
        star.className = 'dynamic-star';

        // 随机位置
        const x = Math.random() * 100;
        const y = Math.random() * 100;
        star.style.left = x + '%';
        star.style.top = y + '%';

        // 随机大小
        const size = Math.random() * 2 + 1; // 1-3px
        star.style.width = size + 'px';
        star.style.height = size + 'px';

        // 随机动画
        const animationType = animations[Math.floor(Math.random() * animations.length)];
        const duration = Math.random() * 4 + 2; // 2-6秒
        const delay = Math.random() * 5; // 0-5秒延迟

        star.style.animation = `${animationType} ${duration}s ease-in-out ${delay}s infinite`;

        // 随机透明度变化
        const maxOpacity = Math.random() * 0.5 + 0.5; // 0.5-1.0
        star.style.setProperty('--max-opacity', maxOpacity);

        starfield.appendChild(star);
    }
}

// 定期重新生成星星位置
function regenerateStarfield() {
    createDynamicStarfield();
    // 每30-60秒重新生成一次
    setTimeout(regenerateStarfield, (Math.random() * 30 + 30) * 1000);
}

// 页面加载完成后初始化星空
document.addEventListener('DOMContentLoaded', function() {
    createDynamicStarfield();
    // 开始定期重新生成星空
    setTimeout(regenerateStarfield, (Math.random() * 30 + 30) * 1000);
});
