<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="记录我们每一次相遇的美好瞬间和珍贵回忆">
    <title>每一次相遇 - Yu 💕 Wang</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💕</text></svg>">
    <link rel="stylesheet" href="/src/client/styles/pages.css">
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&family=Poppins:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 花朵视频背景样式 */
        .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .video-background video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0; /* 初始透明，加载完成后显示 */
            filter: contrast(1.1) saturate(1.1); /* 保持原始亮度，仅增强对比度和饱和度 */
            transition: opacity 1s ease-in-out; /* 平滑过渡效果 */
        }

        /* 视频加载完成后显示 */
        .video-background video.loaded {
            opacity: 1.0; /* 完全不透明，显示视频原始亮度 */
        }

        /* 视频加载时的备用背景 */
        .video-background::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                #0c0c2e 0%,
                #1a1a3e 25%,
                #2d1b69 50%,
                #4a148c 75%,
                #6a1b9a 100%);
            z-index: -1;
            transition: opacity 1s ease-in-out;
        }

        /* 视频加载完成后隐藏备用背景 */
        .video-background.video-loaded::before {
            opacity: 0;
        }

        /* 加载遮罩样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                #0c0c2e 0%,
                #1a1a3e 25%,
                #2d1b69 50%,
                #4a148c 75%,
                #6a1b9a 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.8s ease-out, visibility 0.8s ease-out;
        }

        .loading-overlay.hidden {
            opacity: 0;
            visibility: hidden;
        }

        /* 加载动画 */
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: white;
            font-family: 'Dancing Script', cursive;
            font-size: 1.5rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 10px;
        }

        .loading-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-family: 'Inter', sans-serif;
            font-size: 1rem;
            text-align: center;
        }

        /* 加载进度条 */
        .loading-progress {
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            margin-top: 20px;
            overflow: hidden;
        }

        .loading-progress-bar {
            height: 100%;
            background: white;
            border-radius: 2px;
            width: 0%;
            transition: width 0.3s ease;
            animation: progressPulse 2s ease-in-out infinite;
        }

        @keyframes progressPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* 覆盖原有的body背景，让视频背景显示 */
        body {
            background: transparent !important; /* 移除遮罩，让视频背景完全显示 */
        }

        /* 调整星空效果的透明度，与花朵背景更好融合 */
        body::before {
            opacity: 0.2;
        }

        body::after {
            opacity: 0.15;
        }

        .meeting-card {
            background: linear-gradient(135deg,
                rgba(255, 182, 193, 0.9) 0%,
                rgba(255, 192, 203, 0.8) 25%,
                rgba(255, 218, 185, 0.9) 50%,
                rgba(255, 228, 225, 0.8) 75%,
                rgba(255, 240, 245, 0.9) 100%);
            border-radius: 20px;
            padding: 30px;
            margin: 25px 0;
            box-shadow: 0 20px 40px rgba(255, 105, 180, 0.3),
                        0 0 30px rgba(255, 182, 193, 0.2);
            /* 移除磨砂效果，让背景完全透明 */
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            animation: cardGlow 5s ease-in-out infinite;
        }

        .meeting-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .meeting-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .meeting-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .meeting-number {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Playfair Display', serif;
            font-size: 1.5rem;
            font-weight: 700;
            flex-shrink: 0;
        }
        
        .meeting-info {
            flex: 1;
        }
        
        .meeting-title {
            font-family: 'Playfair Display', serif;
            font-size: 1.6rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .meeting-date {
            font-family: 'Dancing Script', cursive;
            font-size: 1.2rem;
            color: #667eea;
            font-weight: 600;
        }
        
        .meeting-location {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #888;
            margin-bottom: 15px;
            font-size: 0.95rem;
        }
        
        .meeting-description {
            color: #666;
            line-height: 1.7;
            margin-bottom: 20px;
        }
        
        .meeting-highlight {
            background: transparent; /* 完全透明，让花朵背景完全显示 */
            padding: 15px;
            border-radius: 10px;
            font-style: italic;
            color: #555;
        }
        
        .first-meeting {
            background: transparent; /* 完全透明，让花朵背景完全显示 */
            border: 2px solid rgba(240, 147, 251, 0.2);
        }
        
        .first-meeting::before {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            height: 8px;
        }
        
        .special-meeting {
            background: transparent; /* 完全透明，让花朵背景完全显示 */
            border: 2px solid rgba(255, 215, 0, 0.2);
            position: relative;
        }
        
        .special-meeting::before {
            background: linear-gradient(135deg, #ffd700 0%, #ffc107 100%);
            height: 6px;
        }
        
        .special-meeting::after {
            content: '⭐';
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 1.5rem;
            animation: twinkle 2s ease-in-out infinite;
        }
        
        @keyframes twinkle {
            0%, 100% { opacity: 0.6; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.1); }
        }
        
        .meeting-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-item {
            background: linear-gradient(135deg,
                rgba(255, 182, 193, 0.9) 0%,
                rgba(255, 192, 203, 0.8) 25%,
                rgba(255, 218, 185, 0.9) 50%,
                rgba(255, 228, 225, 0.8) 75%,
                rgba(255, 240, 245, 0.9) 100%);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 15px 35px rgba(255, 105, 180, 0.3),
                        0 0 25px rgba(255, 182, 193, 0.2);
            /* 移除磨砂效果，让背景完全透明 */
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
            animation: cardGlow 4s ease-in-out infinite;
        }


        
        .stat-number {
            font-family: 'Playfair Display', serif;
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
        }
        
        .stat-label {
            color: #666;
            font-weight: 500;
            margin-top: 5px;
            font-size: 0.9rem;
        }
        
        .weather-icon {
            display: inline-block;
            margin-left: 5px;
            font-size: 1.1rem;
        }
        
        .mood-indicator {
            display: inline-flex;
            gap: 3px;
            margin-left: 10px;
        }
        
        .mood-heart {
            color: #ff6b9d;
            font-size: 0.9rem;
        }

        /* 增强内容区域的可读性 */
        .content-section {
            /* 移除磨砂效果，让背景完全透明 */
            background: transparent !important;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .page-header {
            /* 移除磨砂效果，让背景完全透明 */
            background: transparent !important;
        }

        .meeting-card {
            /* 移除磨砂效果，让背景完全透明 */
            background: transparent !important;
        }

        .quote-section {
            /* 移除磨砂效果，让背景完全透明 */
            background: transparent !important;
        }
    </style>
</head>
<body>
    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">🌌 加载星河背景中...</div>
        <div class="loading-subtitle">正在为您准备浪漫的相遇回忆</div>
        <div class="loading-progress">
            <div class="loading-progress-bar" id="loadingProgress"></div>
        </div>
    </div>

    <!-- 花朵视频背景 -->
    <div class="video-background">
        <video autoplay muted loop playsinline preload="metadata">
            <source src="/src/client/assets/videos/meetings/meetings.mp4" type="video/mp4">
            <!-- 如果浏览器不支持视频，显示备用背景 -->
            您的浏览器不支持视频播放。
        </video>
    </div>

    <!-- 流星效果 -->
    <div class="shooting-stars">
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
    </div>

    <!-- 浪漫漂浮元素 -->
    <div class="hearts-container">
        <div class="heart type-1 small"></div>
        <div class="heart type-2 medium"></div>
        <div class="heart type-3 large"></div>
        <div class="heart type-4 small"></div>
        <div class="heart type-5 medium"></div>
        <div class="heart type-6 large"></div>
        <div class="heart type-7 small"></div>
        <div class="heart type-8 medium"></div>
        <div class="heart type-9 large"></div>
        <div class="heart type-10 small"></div>
        <div class="heart type-11 medium"></div>
        <div class="heart type-12 large"></div>
        <div class="heart type-1 medium"></div>
        <div class="heart type-3 small"></div>
        <div class="heart type-5 large"></div>
    </div>

    <div class="container">
        <!-- 返回按钮 -->
        <a href="https://liangliangdamowang.edu.deal/love/" class="back-button">
            <i class="fas fa-arrow-left"></i>
            返回主页
        </a>

        <!-- 页面头部 -->
        <header class="page-header">
            <i class="fas fa-heart-circle-plus page-icon"></i>
            <h1 class="page-title">每一次相遇</h1>
            <p class="page-subtitle">记录我们每一次相遇的美好瞬间和珍贵回忆</p>
        </header>

        <!-- 相遇统计 -->
        <section class="content-section">
            <h2 class="section-title">我们的相遇足迹</h2>
            <div class="meeting-stats">
                <div class="stat-item">
                    <span class="stat-number">156</span>
                    <div class="stat-label">次相遇</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">23</span>
                    <div class="stat-label">个地点</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">8</span>
                    <div class="stat-label">个城市</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">365</span>
                    <div class="stat-label">天想念</div>
                </div>
            </div>
        </section>

        <!-- 特殊的相遇 -->
        <section class="content-section">
            <h2 class="section-title">那些特殊的相遇</h2>
            
            <div class="meeting-card first-meeting">
                <div class="meeting-header">
                    <div class="meeting-number">1</div>
                    <div class="meeting-info">
                        <h3 class="meeting-title">初次相遇</h3>
                        <div class="meeting-date">2023年3月15日</div>
                    </div>
                </div>
                <div class="meeting-location">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>星巴克咖啡厅 · 春熙路店</span>
                    <span class="weather-icon">☀️</span>
                    <span class="mood-indicator">
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                    </span>
                </div>
                <p class="meeting-description">
                    那是一个春天的午后，阳光正好，微风不燥。我们在咖啡厅里第一次相遇，
                    你的笑容如春花般绚烂。那一刻，时间仿佛停止了，整个世界都安静下来，
                    只有你的声音在我耳边轻柔地响起。从那一刻起，我就知道你是我要找的那个人。
                </p>
                <div class="meeting-highlight">
                    "有些人一见如故，有些人再见倾心。而你，是我一见倾心，再见倾城。"
                </div>
            </div>

            <div class="meeting-card special-meeting">
                <div class="meeting-header">
                    <div class="meeting-number">7</div>
                    <div class="meeting-info">
                        <h3 class="meeting-title">确定关系的那天</h3>
                        <div class="meeting-date">2023年4月23日</div>
                    </div>
                </div>
                <div class="meeting-location">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>人民公园 · 樱花大道</span>
                    <span class="weather-icon">🌸</span>
                    <span class="mood-indicator">
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                    </span>
                </div>
                <p class="meeting-description">
                    樱花飞舞的季节，我们走在樱花大道上。粉色的花瓣如雪花般飘落，
                    你突然停下脚步，认真地看着我说："做我的女朋友好吗？"
                    那一刻，我的心跳得很快，脸红得像樱花一样。这是我们最重要的一次相遇。
                </p>
                <div class="meeting-highlight">
                    "在樱花飞舞的季节里，我们的爱情也开始绽放。"
                </div>
            </div>

            <div class="meeting-card">
                <div class="meeting-header">
                    <div class="meeting-number">15</div>
                    <div class="meeting-info">
                        <h3 class="meeting-title">第一次旅行</h3>
                        <div class="meeting-date">2023年8月20日</div>
                    </div>
                </div>
                <div class="meeting-location">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>青岛 · 金沙滩</span>
                    <span class="weather-icon">🌅</span>
                    <span class="mood-indicator">
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                    </span>
                </div>
                <p class="meeting-description">
                    我们的第一次旅行，一起去看海。清晨5点起床去看日出，
                    海风轻抚着我们的脸庞，金色的阳光洒在海面上。你牵着我的手说：
                    "希望我们能像这日出一样，每天都有新的开始。"
                </p>
                <div class="meeting-highlight">
                    "那是我见过最美的日出，因为有你在身边。"
                </div>
            </div>

            <div class="meeting-card">
                <div class="meeting-header">
                    <div class="meeting-number">42</div>
                    <div class="meeting-info">
                        <h3 class="meeting-title">雨中漫步</h3>
                        <div class="meeting-date">2023年11月8日</div>
                    </div>
                </div>
                <div class="meeting-location">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>锦里古街</span>
                    <span class="weather-icon">🌧️</span>
                    <span class="mood-indicator">
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                    </span>
                </div>
                <p class="meeting-description">
                    那天突然下起了雨，我们没有带伞，却在雨中慢慢走着。
                    你说这样的雨天很浪漫，我觉得有你在身边就是浪漫。
                    雨水打湿了我们的衣服，但我们的心却是温暖的。
                </p>
                <div class="meeting-highlight">
                    "最浪漫的不是雨天，而是和你一起躲过雨的屋檐。"
                </div>
            </div>

            <div class="meeting-card">
                <div class="meeting-header">
                    <div class="meeting-number">88</div>
                    <div class="meeting-info">
                        <h3 class="meeting-title">深夜的车站</h3>
                        <div class="meeting-date">2024年2月14日</div>
                    </div>
                </div>
                <div class="meeting-location">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>成都东站</span>
                    <span class="weather-icon">🌙</span>
                    <span class="mood-indicator">
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                        <i class="fas fa-heart mood-heart"></i>
                    </span>
                </div>
                <p class="meeting-description">
                    情人节的深夜，你要坐火车回家。我们在车站拥抱告别，
                    那一刻真的很舍不得。你说："距离不会让我们的爱变淡，
                    只会让我们更加珍惜每一次相遇。"
                </p>
                <div class="meeting-highlight">
                    "离别是为了更好的相遇，思念让爱情更加珍贵。"
                </div>
            </div>

        </section>

        <!-- 日常的相遇 -->
        <section class="content-section">
            <h2 class="section-title">日常的美好相遇</h2>
            <div class="memory-grid">
                <div class="memory-card">
                    <div class="memory-card-icon">
                        <i class="fas fa-coffee"></i>
                    </div>
                    <h3 class="memory-card-title">周末咖啡时光</h3>
                    <p class="memory-card-content">
                        每个周末的下午，我们都会在那家小咖啡店相遇。
                        你总是比我早到，为我点好最爱的拿铁。
                    </p>
                </div>

                <div class="memory-card">
                    <div class="memory-card-icon">
                        <i class="fas fa-film"></i>
                    </div>
                    <h3 class="memory-card-title">电影院约会</h3>
                    <p class="memory-card-content">
                        我们一起看过很多电影，从浪漫爱情片到惊险动作片。
                        每次都是美好的约会时光。
                    </p>
                </div>

                <div class="memory-card">
                    <div class="memory-card-icon">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <h3 class="memory-card-title">美食探索</h3>
                    <p class="memory-card-content">
                        我们一起尝遍了城市里的各种美食，
                        每一次都是新的发现和惊喜。
                    </p>
                </div>

                <div class="memory-card">
                    <div class="memory-card-icon">
                        <i class="fas fa-tree"></i>
                    </div>
                    <h3 class="memory-card-title">公园散步</h3>
                    <p class="memory-card-content">
                        在公园里慢慢走着，聊着天南海北的话题，
                        简单却充满幸福的时光。
                    </p>
                </div>
            </div>
        </section>

        <!-- 爱情感悟 -->
        <section class="quote-section">
            <p class="quote-text">
                "每一次相遇都是缘分的安排，每一个瞬间都值得珍藏。
                愿我们能够一直这样，在人生的路上不断相遇，不断创造美好的回忆。"
            </p>
            <p class="quote-author">— 我们的相遇感悟</p>
        </section>

        <!-- 未来的相遇 -->
        <section class="content-section">
            <h2 class="section-title">期待未来的相遇</h2>
            <div class="section-content">
                <p>
                    还有很多美好的相遇等待着我们：在教堂里的相遇，在新家里的相遇，
                    在孩子出生时的相遇...每一次相遇都将是我们爱情故事的新篇章。
                </p>
                <br>
                <p>
                    愿我们能够一直这样，在人生的每一个重要时刻相遇，
                    在平凡的日子里相遇，在未来的每一天里相遇。
                    因为有你，每一次相遇都是最美的风景。
                </p>
            </div>
        </section>
    </div>

    <script>
        // 花朵视频背景加载处理
        document.addEventListener('DOMContentLoaded', function() {
            const video = document.querySelector('.video-background video');
            const videoContainer = document.querySelector('.video-background');
            const loadingOverlay = document.getElementById('loadingOverlay');
            const loadingProgress = document.getElementById('loadingProgress');

            let progressInterval;
            let currentProgress = 0;

            // 视频加载状态标记
            let videoInitialized = false;
            let videoLoadingStarted = false;

            // 模拟加载进度
            function updateProgress() {
                if (currentProgress < 90) {
                    currentProgress += Math.random() * 15;
                    if (currentProgress > 90) currentProgress = 90;
                    loadingProgress.style.width = currentProgress + '%';
                }
            }

            // 隐藏加载遮罩
            function hideLoadingOverlay() {
                currentProgress = 100;
                loadingProgress.style.width = '100%';
                setTimeout(function() {
                    loadingOverlay.classList.add('hidden');
                }, 300);
            }

            if (video) {
                // 开始进度模拟
                progressInterval = setInterval(updateProgress, 200);

                // 视频开始加载
                video.addEventListener('loadstart', function() {
                    if (!videoLoadingStarted) {
                        console.log('星河视频开始加载...');
                        videoLoadingStarted = true;
                    }
                });

                // 视频加载进度
                video.addEventListener('progress', function() {
                    if (!videoInitialized && video.buffered.length > 0) {
                        const buffered = video.buffered.end(0);
                        const duration = video.duration;
                        if (duration > 0) {
                            const realProgress = (buffered / duration) * 90;
                            if (realProgress > currentProgress) {
                                currentProgress = realProgress;
                                loadingProgress.style.width = currentProgress + '%';
                            }
                        }
                    }
                });

                // 视频有足够数据可以播放
                video.addEventListener('canplay', function() {
                    if (!videoInitialized) {
                        console.log('星河视频可以播放');
                        clearInterval(progressInterval);
                        currentProgress = 95;
                        loadingProgress.style.width = currentProgress + '%';

                        // 确保视频自动播放
                        video.play().catch(function(error) {
                            console.log('视频自动播放被阻止:', error);
                            // 即使播放失败也隐藏加载遮罩
                            hideLoadingOverlay();
                        });
                    }
                });

                // 视频加载完成并开始播放
                video.addEventListener('playing', function() {
                    if (!videoInitialized) {
                        console.log('星河视频背景加载成功并开始播放');
                        clearInterval(progressInterval);
                        video.classList.add('loaded');
                        videoContainer.classList.add('video-loaded');
                        hideLoadingOverlay();
                        videoInitialized = true; // 标记视频已初始化
                    }
                });

                // 视频加载失败
                video.addEventListener('error', function() {
                    if (!videoInitialized) {
                        console.log('星河视频背景加载失败，使用星河主题备用背景');
                        clearInterval(progressInterval);
                        video.style.display = 'none';
                        videoContainer.style.background = 'linear-gradient(135deg, #0c0c2e 0%, #1a1a3e 25%, #2d1b69 50%, #4a148c 75%, #6a1b9a 100%)';
                        videoContainer.classList.add('video-loaded');
                        hideLoadingOverlay();
                        videoInitialized = true;
                    }
                });

                // 设置超时，如果视频加载时间过长，显示星河主题背景
                setTimeout(function() {
                    if (!video.classList.contains('loaded')) {
                        console.log('视频加载超时，切换到星河主题背景');
                        clearInterval(progressInterval);
                        videoContainer.style.background = 'linear-gradient(135deg, #0c0c2e 0%, #1a1a3e 25%, #2d1b69 50%, #4a148c 75%, #6a1b9a 100%)';
                        videoContainer.classList.add('video-loaded');
                        hideLoadingOverlay();
                    }
                }, 10000); // 10秒超时
            } else {
                // 如果没有视频元素，直接隐藏加载遮罩
                hideLoadingOverlay();
            }

            // 页面卸载时清理视频资源
            window.addEventListener('beforeunload', function() {
                const video = document.querySelector('.video-background video');
                if (video) {
                    video.pause();
                    video.src = '';
                    video.load();
                    console.log('🧹 页面卸载，已清理视频资源');
                }
            });

            // 页面隐藏时暂停视频，显示时恢复播放
            document.addEventListener('visibilitychange', function() {
                const video = document.querySelector('.video-background video');
                if (video && videoInitialized) {
                    if (document.hidden) {
                        video.pause();
                        console.log('📱 页面隐藏，暂停视频播放');
                    } else {
                        video.play().catch(function(error) {
                            console.log('📱 页面显示，恢复视频播放失败:', error);
                        });
                        console.log('📱 页面显示，恢复视频播放');
                    }
                }
            });
        });
    </script>
</body>
</html>
