#!/bin/bash

# Love项目综合管理脚本 v2.0
# 功能：启动/停止/重启、数据推送、视频压缩上传、系统管理
# 作者：Love项目团队
# 更新：2025-07-31

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="Love"
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$PROJECT_DIR/.love.pid"
LOG_FILE="$PROJECT_DIR/logs/love-manager.log"
CONFIG_FILE="$PROJECT_DIR/config/config.js"
ENV_FILE="$PROJECT_DIR/config/.env"

# Git配置
PRIVATE_REPO="**************:your-username/love-private.git"  # 请修改为您的私有仓库地址
BACKUP_BRANCH="main"

# 创建日志目录
mkdir -p "$PROJECT_DIR/logs"

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1" | tee -a "$LOG_FILE"
}

# 检查依赖
check_dependencies() {
    local deps=("node" "npm" "git" "ffmpeg")
    local missing=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing+=("$dep")
        fi
    done
    
    if [ ${#missing[@]} -ne 0 ]; then
        error "缺少依赖: ${missing[*]}"
        error "请安装缺少的依赖后重试"
        exit 1
    fi
}

# 检查项目状态
check_project_status() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "running"
            return 0
        else
            rm -f "$PID_FILE"
            echo "stopped"
            return 1
        fi
    else
        echo "stopped"
        return 1
    fi
}

# 启动项目
start_project() {
    log "🚀 启动Love项目..."
    
    local status=$(check_project_status)
    if [ "$status" = "running" ]; then
        warning "项目已经在运行中"
        return 0
    fi
    
    # 检查配置文件
    if [ ! -f "$CONFIG_FILE" ]; then
        error "配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    
    if [ ! -f "$ENV_FILE" ]; then
        error "环境变量文件不存在: $ENV_FILE"
        exit 1
    fi
    
    # 安装依赖
    info "检查并安装依赖..."
    cd "$PROJECT_DIR"
    npm install --production
    
    # 启动服务
    info "启动Node.js服务..."
    nohup node server.js > "$PROJECT_DIR/logs/server.log" 2>&1 &
    local pid=$!
    echo "$pid" > "$PID_FILE"
    
    # 等待启动
    sleep 3
    if ps -p "$pid" > /dev/null 2>&1; then
        log "✅ Love项目启动成功 (PID: $pid)"
        info "访问地址: http://localhost:$(grep PORT $ENV_FILE | cut -d'=' -f2)"
    else
        error "❌ Love项目启动失败"
        rm -f "$PID_FILE"
        exit 1
    fi
}

# 停止项目
stop_project() {
    log "🛑 停止Love项目..."
    
    local status=$(check_project_status)
    if [ "$status" = "stopped" ]; then
        warning "项目已经停止"
        return 0
    fi
    
    local pid=$(cat "$PID_FILE")
    info "终止进程 PID: $pid"
    
    # 优雅停止
    kill -TERM "$pid" 2>/dev/null || true
    sleep 5
    
    # 强制停止
    if ps -p "$pid" > /dev/null 2>&1; then
        warning "优雅停止失败，强制终止..."
        kill -KILL "$pid" 2>/dev/null || true
        sleep 2
    fi
    
    rm -f "$PID_FILE"
    log "✅ Love项目已停止"
}

# 重启项目
restart_project() {
    log "🔄 重启Love项目..."
    stop_project
    sleep 2
    start_project
}

# 显示项目状态
show_status() {
    echo -e "\n${CYAN}=== Love项目状态 ===${NC}"
    
    local status=$(check_project_status)
    if [ "$status" = "running" ]; then
        local pid=$(cat "$PID_FILE")
        echo -e "状态: ${GREEN}运行中${NC} (PID: $pid)"
        
        # 显示内存使用
        local memory=$(ps -p "$pid" -o rss= 2>/dev/null | awk '{print int($1/1024)"MB"}' || echo "N/A")
        echo -e "内存使用: $memory"
        
        # 显示运行时间
        local start_time=$(ps -p "$pid" -o lstart= 2>/dev/null || echo "N/A")
        echo -e "启动时间: $start_time"
    else
        echo -e "状态: ${RED}已停止${NC}"
    fi
    
    # 显示端口信息
    local port=$(grep PORT "$ENV_FILE" 2>/dev/null | cut -d'=' -f2 || echo "3000")
    echo -e "端口: $port"
    
    # 显示最近日志
    echo -e "\n${CYAN}=== 最近日志 ===${NC}"
    if [ -f "$PROJECT_DIR/logs/server.log" ]; then
        tail -5 "$PROJECT_DIR/logs/server.log"
    else
        echo "暂无日志"
    fi
}

# 数据推送到私有库
push_to_private() {
    log "📤 开始推送数据到私有库..."

    # 检查Git配置
    if [ ! -d "$PROJECT_DIR/.git" ]; then
        error "当前目录不是Git仓库"
        exit 1
    fi

    # 检查私有仓库配置
    if [ "$PRIVATE_REPO" = "**************:your-username/love-private.git" ]; then
        error "请先配置私有仓库地址 (PRIVATE_REPO变量)"
        exit 1
    fi

    cd "$PROJECT_DIR"

    # 添加私有仓库远程地址
    if ! git remote get-url private &>/dev/null; then
        info "添加私有仓库远程地址..."
        git remote add private "$PRIVATE_REPO"
    fi

    # 创建备份分支
    local backup_branch="backup-$(date +%Y%m%d-%H%M%S)"
    info "创建备份分支: $backup_branch"
    git checkout -b "$backup_branch" 2>/dev/null || git checkout "$backup_branch"

    # 添加所有文件（包括敏感数据）
    info "添加所有文件到Git..."

    # 临时修改.gitignore以包含敏感文件
    local gitignore_backup=""
    if [ -f ".gitignore" ]; then
        gitignore_backup=$(cat .gitignore)
        # 注释掉敏感文件的忽略规则
        sed -i 's/^\.env/#\.env/g' .gitignore
        sed -i 's/^\*\.env/#\*\.env/g' .gitignore
        sed -i 's/^config\/\.env/#config\/\.env/g' .gitignore
    fi

    # 添加所有文件
    git add .
    git add -f config/.env 2>/dev/null || true
    git add -f .env 2>/dev/null || true
    git add -f src/client/assets/videos/ 2>/dev/null || true
    git add -f src/client/assets/videos-compressed/ 2>/dev/null || true
    git add -f cloudinary-upload/ 2>/dev/null || true

    # 恢复.gitignore
    if [ -n "$gitignore_backup" ]; then
        echo "$gitignore_backup" > .gitignore
        git add .gitignore
    fi

    # 提交更改
    local commit_msg="Backup: $(date '+%Y-%m-%d %H:%M:%S') - 包含视频文件和敏感数据"
    info "提交更改: $commit_msg"
    git commit -m "$commit_msg" || warning "没有新的更改需要提交"

    # 推送到私有仓库
    info "推送到私有仓库..."
    if git push private "$backup_branch"; then
        log "✅ 数据推送成功到私有库分支: $backup_branch"

        # 推送到主分支
        info "推送到主分支..."
        git push private "$backup_branch:$BACKUP_BRANCH" --force
        log "✅ 数据推送成功到主分支: $BACKUP_BRANCH"
    else
        error "❌ 数据推送失败"
        exit 1
    fi

    # 返回原分支
    git checkout main 2>/dev/null || git checkout master 2>/dev/null || true

    log "✅ 数据推送完成"
}

# 压缩视频并上传到Cloudinary
compress_and_upload() {
    log "🎬 开始视频压缩和Cloudinary上传..."

    # 检查ffmpeg
    if ! command -v ffmpeg &> /dev/null; then
        error "ffmpeg未安装，无法进行视频压缩"
        exit 1
    fi

    # 检查Cloudinary配置
    if ! node -e "require('./config/config.js')" &>/dev/null; then
        error "配置文件加载失败，请检查config.js"
        exit 1
    fi

    cd "$PROJECT_DIR"

    # 运行压缩脚本
    info "执行高画质视频压缩..."
    if [ -f "cloudinary/compress-videos-for-cloudinary.sh" ]; then
        chmod +x cloudinary/compress-videos-for-cloudinary.sh
        ./cloudinary/compress-videos-for-cloudinary.sh
    else
        error "压缩脚本不存在: cloudinary/compress-videos-for-cloudinary.sh"
        exit 1
    fi

    # 清理Cloudinary账户
    info "清理Cloudinary账户..."
    if [ -f "cloudinary/cleanup-accounts.js" ]; then
        node cloudinary/cleanup-accounts.js --confirm
    else
        warning "清理脚本不存在，跳过清理步骤"
    fi

    # 上传到Cloudinary
    info "上传视频到Cloudinary多账户..."
    if [ -f "cloudinary/upload-multi-account.js" ]; then
        node cloudinary/upload-multi-account.js
    else
        error "上传脚本不存在: cloudinary/upload-multi-account.js"
        exit 1
    fi

    # 删除服务器端同名文件
    info "删除服务器端原始视频文件..."
    if [ -d "src/client/assets/videos" ]; then
        find src/client/assets/videos -name "*.mp4" -type f -exec rm -f {} \;
        log "✅ 原始视频文件已删除"
    fi

    # 保留压缩后的本地备份
    info "保留压缩后的本地备份文件..."
    if [ -d "src/client/assets/videos-compressed" ]; then
        log "✅ 压缩后的备份文件保留在: src/client/assets/videos-compressed/"
    fi

    log "✅ 视频压缩和上传完成"
}

# 系统维护
system_maintenance() {
    log "🔧 开始系统维护..."

    cd "$PROJECT_DIR"

    # 清理日志文件
    info "清理旧日志文件..."
    find logs/ -name "*.log" -mtime +7 -delete 2>/dev/null || true

    # 清理临时文件
    info "清理临时文件..."
    rm -rf tmp/ temp/ .tmp/ 2>/dev/null || true

    # 清理node_modules缓存
    info "清理npm缓存..."
    npm cache clean --force 2>/dev/null || true

    # 更新依赖
    info "检查依赖更新..."
    npm audit fix --force 2>/dev/null || warning "依赖更新失败"

    # 检查磁盘空间
    info "检查磁盘空间..."
    local disk_usage=$(df -h "$PROJECT_DIR" | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 80 ]; then
        warning "磁盘使用率过高: ${disk_usage}%"
    else
        info "磁盘使用率正常: ${disk_usage}%"
    fi

    log "✅ 系统维护完成"
}

# 环境检查
check_environment() {
    log "🔍 开始环境检查..."

    cd "$PROJECT_DIR"

    # 运行环境检查脚本
    if [ -f "cloudinary/environment-check.js" ]; then
        node cloudinary/environment-check.js
    else
        warning "环境检查脚本不存在"
    fi

    # 检查配置文件
    info "检查配置文件..."
    if [ -f "$CONFIG_FILE" ] && [ -f "$ENV_FILE" ]; then
        log "✅ 配置文件完整"
    else
        error "❌ 配置文件缺失"
        exit 1
    fi

    # 检查端口占用
    local port=$(grep PORT "$ENV_FILE" 2>/dev/null | cut -d'=' -f2 || echo "3000")
    if netstat -tuln 2>/dev/null | grep ":$port " >/dev/null; then
        warning "端口 $port 已被占用"
    else
        info "端口 $port 可用"
    fi

    log "✅ 环境检查完成"
}

# 显示帮助信息
show_help() {
    echo -e "\n${CYAN}Love项目综合管理脚本 v2.0${NC}"
    echo -e "${CYAN}================================${NC}\n"

    echo -e "${YELLOW}用法:${NC}"
    echo -e "  $0 <命令> [选项]\n"

    echo -e "${YELLOW}命令:${NC}"
    echo -e "  ${GREEN}start${NC}              启动Love项目"
    echo -e "  ${GREEN}stop${NC}               停止Love项目"
    echo -e "  ${GREEN}restart${NC}            重启Love项目"
    echo -e "  ${GREEN}status${NC}             显示项目状态"
    echo -e "  ${GREEN}push${NC}               推送数据到私有库（包含敏感数据）"
    echo -e "  ${GREEN}compress${NC}           压缩视频并上传到Cloudinary"
    echo -e "  ${GREEN}maintenance${NC}        执行系统维护"
    echo -e "  ${GREEN}check${NC}              环境检查"
    echo -e "  ${GREEN}help${NC}               显示此帮助信息\n"

    echo -e "${YELLOW}示例:${NC}"
    echo -e "  $0 start               # 启动项目"
    echo -e "  $0 push                # 推送所有数据到私有库"
    echo -e "  $0 compress            # 压缩视频并上传"
    echo -e "  $0 restart             # 重启项目\n"

    echo -e "${YELLOW}注意事项:${NC}"
    echo -e "  • 请先配置私有仓库地址 (PRIVATE_REPO变量)"
    echo -e "  • 确保已安装所需依赖: node, npm, git, ffmpeg"
    echo -e "  • push命令会包含.env等敏感文件"
    echo -e "  • compress命令会删除原始视频文件\n"
}

# 主函数
main() {
    # 检查参数
    if [ $# -eq 0 ]; then
        show_help
        exit 1
    fi

    # 检查依赖
    check_dependencies

    # 执行命令
    case "$1" in
        "start")
            start_project
            ;;
        "stop")
            stop_project
            ;;
        "restart")
            restart_project
            ;;
        "status")
            show_status
            ;;
        "push")
            push_to_private
            ;;
        "compress")
            compress_and_upload
            ;;
        "maintenance")
            system_maintenance
            ;;
        "check")
            check_environment
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
