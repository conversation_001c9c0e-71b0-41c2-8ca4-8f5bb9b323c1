# Love项目综合管理脚本使用指南

## 📋 概述

`love-manager.sh` 是Love项目的综合管理脚本，提供项目启动/停止、数据推送、视频压缩上传、系统维护等一站式管理功能。

## 🚀 快速开始

### 基本使用

```bash
# 显示帮助信息
./love-manager.sh help

# 检查环境
./love-manager.sh check

# 查看项目状态
./love-manager.sh status

# 启动项目
./love-manager.sh start

# 停止项目
./love-manager.sh stop

# 重启项目
./love-manager.sh restart
```

## 🎬 视频压缩和Cloudinary上传

### 压缩并上传视频

```bash
# 执行完整的视频压缩和上传流程
./love-manager.sh compress
```

**功能说明**：
- 使用方案A高画质压缩参数（CRF 18, veryslow, 2560x1440）
- 自动清理Cloudinary账户中的同名文件
- 按页面映射上传到对应的Cloudinary账户
- 删除服务器端原始视频文件（保留压缩后的本地备份）

**压缩效果预期**：
- anniversary.mp4: 570MB → ~95MB
- together-days.mp4: 146MB → ~75MB
- memorial.mp4: 93MB → ~65MB
- home.mp4: 63MB → ~45MB
- meetings.mp4: 39MB → ~30MB

## 📤 数据推送到私有库

### 推送所有数据（包含敏感信息）

```bash
# 推送所有文件到私有仓库
./love-manager.sh push
```

**功能说明**：
- 推送所有项目文件，包括视频文件和敏感数据
- 自动包含 `.env` 等通常被忽略的敏感文件
- 创建带时间戳的备份分支
- 推送到私有仓库的主分支

**⚠️ 重要提醒**：
- 请先配置私有仓库地址（修改脚本中的 `PRIVATE_REPO` 变量）
- 确保私有仓库的访问权限已正确配置
- 此操作会包含所有敏感信息，请确保仓库安全

### 配置私有仓库

编辑 `love-manager.sh` 文件，修改以下行：

```bash
# 将此行修改为您的私有仓库地址
PRIVATE_REPO="**************:your-username/love-private.git"
```

## 🔧 系统维护

### 执行系统维护

```bash
# 执行系统维护任务
./love-manager.sh maintenance
```

**功能说明**：
- 清理7天以上的旧日志文件
- 清理临时文件和缓存
- 更新npm依赖
- 检查磁盘空间使用情况

## 📊 环境检查

### 全面环境检查

```bash
# 检查系统环境
./love-manager.sh check
```

**检查项目**：
- 项目文件结构完整性
- Node.js依赖安装状态
- FFmpeg工具可用性
- 配置文件正确性
- 环境变量设置
- Cloudinary API连接状态

## 🎯 Cloudinary多账户配置

### 账户映射关系

| 页面 | Cloudinary账户 | Cloud Name | 用途 |
|------|----------------|------------|------|
| home | YU0 | dcglebc2w | 首页背景视频 |
| anniversary | YU1 | drhqbbqxz | 纪念日页面 |
| meetings | YU2 | dkqnm9nwr | 相遇回忆页面 |
| memorial | YU3 | ds14sv2gh | 纪念相册页面 |
| together-days | YU4 | dpq95x5nf | 在一起的日子 |
| backup | YU5 | dtsgvqrna | 备用账户 |

### 方案A压缩参数

```bash
# 固定压缩参数（视觉无损）
CRF: 18
预设: veryslow
分辨率: 2560x1440
音频码率: 256k
像素格式: yuv420p
```

## 📁 文件结构

### 压缩后的文件位置

```
love/
├── src/client/assets/videos/           # 原始视频文件
├── src/client/assets/videos-compressed/ # 压缩后本地备份
├── cloudinary-upload/                  # 上传准备目录
├── logs/                              # 日志文件
├── config/.env                        # 环境变量配置
└── love-manager.sh                    # 综合管理脚本
```

## 🔍 故障排除

### 常见问题

**1. 压缩失败**
```bash
# 检查ffmpeg是否安装
ffmpeg -version

# 检查磁盘空间
df -h
```

**2. 上传失败**
```bash
# 检查网络连接
./love-manager.sh check

# 验证API密钥
node cloudinary/test-connection.js
```

**3. 推送失败**
```bash
# 检查Git配置
git remote -v

# 检查SSH密钥
ssh -T **************
```

## 📝 日志管理

### 日志文件位置

- **管理脚本日志**: `logs/love-manager.log`
- **服务器日志**: `logs/server.log`
- **压缩过程日志**: 实时显示在终端

### 查看日志

```bash
# 查看管理脚本日志
tail -f logs/love-manager.log

# 查看服务器日志
tail -f logs/server.log
```

## ⚡ 高级用法

### 批量操作示例

```bash
# 完整的部署流程
./love-manager.sh check          # 1. 环境检查
./love-manager.sh compress       # 2. 压缩上传
./love-manager.sh push           # 3. 推送备份
./love-manager.sh restart        # 4. 重启服务
```

### 定时任务设置

```bash
# 添加到crontab，每天凌晨2点执行维护
0 2 * * * /path/to/love/love-manager.sh maintenance
```

## 🛡️ 安全注意事项

1. **敏感信息保护**：
   - `.env` 文件包含API密钥，请妥善保管
   - 私有仓库推送包含敏感数据，确保仓库安全

2. **备份策略**：
   - 压缩前会保留原始文件备份
   - 推送前会创建Git备份分支

3. **权限管理**：
   - 确保脚本有执行权限：`chmod +x love-manager.sh`
   - 确保有足够的磁盘空间进行压缩操作

## 📞 技术支持

如遇到问题，请：

1. 首先运行 `./love-manager.sh check` 进行环境检查
2. 查看相关日志文件
3. 检查网络连接和API配置
4. 确认磁盘空间充足

---

**Love项目团队** | 更新时间：2025-07-31
