# Love项目 - 宝塔面板配置说明
#
# 注意：此项目使用宝塔面板进行域名和反向代理管理
# 请不要直接使用此配置文件覆盖宝塔面板的配置
#
# 宝塔面板配置信息：
# - 域名：love.yuh.cool
# - 反向代理目标：http://127.0.0.1:1314
# - 访问方式：love.yuh.cool/api, love.yuh.cool/health 等
#
# 配置生成时间: 2025-07-31
# 项目端口: 1314
# 管理方式: 宝塔面板

# ============================================
# 宝塔面板推荐配置说明
# ============================================

# 1. 站点设置：
#    - 域名：love.yuh.cool
#    - 根目录：可设置为 /root/workspace/love（可选）
#    - PHP版本：不需要（Node.js项目）

# 2. 反向代理设置：
#    - 代理名称：Love项目
#    - 目标URL：http://127.0.0.1:1314
#    - 发送域名：$host
#    - 缓存：可选择开启静态文件缓存

# 3. SSL证书：
#    - 通过宝塔面板申请Let's Encrypt证书
#    - 域名：love.yuh.cool
#    - 自动续期：建议开启

# 4. 访问路径说明：
#    - 主页：https://love.yuh.cool/
#    - API接口：https://love.yuh.cool/api/
#    - 健康检查：https://love.yuh.cool/api/health
#    - 静态资源：https://love.yuh.cool/style.css 等

# ============================================
# 项目特殊需求说明
# ============================================

# Love项目的特殊路径：
# - /api/* : API接口，需要代理到后端
# - /html/* : 静态HTML页面
# - /background/* : 背景资源文件
# - /fonts/* : 字体文件
# - /*.css, /*.js : 样式和脚本文件

# 建议的宝塔面板反向代理配置：
# 目标URL: http://127.0.0.1:1314
# 发送域名: $host
# 替换响应内容: 不需要
# 缓存: 可开启静态文件缓存（.css, .js, .png, .jpg等）

# ============================================
# 故障排除说明
# ============================================

# 如果遇到问题，请检查：
# 1. Love服务是否在1314端口正常运行
# 2. 宝塔面板的反向代理配置是否正确
# 3. 域名DNS解析是否指向服务器
# 4. SSL证书是否有效
# 5. 防火墙是否允许相关端口

# 测试命令：
# curl http://localhost:1314/api/health  # 测试本地服务
# curl https://love.yuh.cool/api/health  # 测试域名访问

# ============================================
# 维护说明
# ============================================

# 此文件主要用于：
# 1. 记录项目的nginx配置需求
# 2. 为manage.sh脚本提供引用（避免脚本报错）
# 3. 作为宝塔面板配置的参考文档
# 4. 项目部署和维护的说明文档

# 注意：
# - 不要直接使用此文件替换宝塔面板的nginx配置
# - 所有nginx相关操作请通过宝塔面板进行
# - 如需修改配置，请在宝塔面板中操作