# Love项目环境配置
# 此文件包含敏感配置信息，不应提交到版本控制系统

# 运行环境配置
NODE_ENV=production

# 服务器配置
PORT=1314

# 域名配置
BASE_DOMAIN=love.yuh.cool
BASE_URL=https://love.yuh.cool

# 数据库配置
DB_PATH=./data/love_messages.db
DB_BACKUP_DIR=./data/backups

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/backend.log

# 服务配置
CORS_ORIGIN=*
STATIC_FILES_CACHE=3600

# 特殊日期配置 (用于生日倒计时)
YU_BIRTHDAY=01-16
WANG_BIRTHDAY=04-15
LOVE_START_DATE=2023-01-01

# API配置
API_PREFIX=/api
MESSAGES_ENDPOINT=/messages
HEALTH_ENDPOINT=/health

# Cloudinary多账户配置 (方案A - 高画质)
CLOUDINARY_ENABLED=true

# Cloudinary API密钥 (YU0-YU5命名规则) - 真实密钥
# YU0: dcglebc2w (首页)
CLOUDINARY_SECRET_YU0="FfwmlQJX_0LOszwF6YF9KbnhmoU"

# YU1: drhqbbqxz (纪念日)
CLOUDINARY_SECRET_YU1="7g-JSBacW-ccz1cSAdkHw_wCrU8"

# YU2: dkqnm9nwr (相遇回忆)
CLOUDINARY_SECRET_YU2="juh_-_Amw-ds0gY03QL-E88oOIQ"

# YU3: ds14sv2gh (纪念相册)
CLOUDINARY_SECRET_YU3="ajE1x9E4Ynrg5AioDxJC_EZuTow"

# YU4: dpq95x5nf (在一起的日子)
CLOUDINARY_SECRET_YU4="849z0GBq5fapCwUCaZ0Ct0H4-5Y"

# YU5: dtsgvqrna (备用账户)
CLOUDINARY_SECRET_YU5="wgHrEwcNyzFyOceB9Q9yAHbteqc"
