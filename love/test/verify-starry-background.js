/**
 * 星空背景保障机制验证脚本
 * 验证第四层CDN降级保障的功能完整性
 */

const fs = require('fs');
const path = require('path');

console.log('🌟 星空背景保障机制验证开始...\n');

// 验证结果收集
const verificationResults = {
    files: {},
    functionality: {},
    integration: {},
    performance: {}
};

// 1. 文件存在性验证
console.log('📁 1. 文件存在性验证');
const requiredFiles = {
    'starry-background.css': 'src/client/styles/starry-background.css',
    'video-loader.js': 'src/client/scripts/video-loader.js',
    'test-starry-background.html': 'test/test-starry-background.html',
    'starry-background-guide.md': 'docs/starry-background-guide.md',
    'starry-bg.svg': 'src/client/assets/images/starry-bg.svg'
};

for (const [name, filePath] of Object.entries(requiredFiles)) {
    const fullPath = path.join(__dirname, '..', filePath);
    const exists = fs.existsSync(fullPath);
    verificationResults.files[name] = exists;
    
    if (exists) {
        const stats = fs.statSync(fullPath);
        console.log(`  ✅ ${name}: 存在 (${(stats.size / 1024).toFixed(2)}KB)`);
    } else {
        console.log(`  ❌ ${name}: 不存在`);
    }
}

// 2. CSS功能验证
console.log('\n🎨 2. CSS功能验证');
const cssPath = path.join(__dirname, '..', 'src/client/styles/starry-background.css');
if (fs.existsSync(cssPath)) {
    const cssContent = fs.readFileSync(cssPath, 'utf8');
    
    const cssChecks = {
        '基础星空背景类': cssContent.includes('.starry-background'),
        '页面主题样式': cssContent.includes('.starry-background.home'),
        '动画定义': cssContent.includes('@keyframes starryMove'),
        '响应式设计': cssContent.includes('@media'),
        '性能优化': cssContent.includes('will-change'),
        '无障碍支持': cssContent.includes('prefers-reduced-motion')
    };
    
    for (const [check, passed] of Object.entries(cssChecks)) {
        verificationResults.functionality[check] = passed;
        console.log(`  ${passed ? '✅' : '❌'} ${check}`);
    }
    
    // 统计信息
    const themeCount = (cssContent.match(/\.starry-background\.[a-z-]+\s*{/g) || []).length;
    const animationCount = (cssContent.match(/@keyframes/g) || []).length;
    const mediaQueryCount = (cssContent.match(/@media/g) || []).length;
    
    console.log(`  📊 统计: ${themeCount}个主题, ${animationCount}个动画, ${mediaQueryCount}个响应式断点`);
}

// 3. JavaScript功能验证
console.log('\n🔧 3. JavaScript功能验证');
const jsPath = path.join(__dirname, '..', 'src/client/scripts/video-loader.js');
if (fs.existsSync(jsPath)) {
    const jsContent = fs.readFileSync(jsPath, 'utf8');
    
    const jsChecks = {
        'loadStarryBackground方法': jsContent.includes('loadStarryBackground'),
        'ensureStarryBackgroundCSS方法': jsContent.includes('ensureStarryBackgroundCSS'),
        '主题背景映射': jsContent.includes('themeBackgrounds'),
        'quaternary层级支持': jsContent.includes('quaternary'),
        '星空加载指示器': jsContent.includes('addStarryLoadingIndicator'),
        '事件记录功能': jsContent.includes('logStarryBackgroundEvent'),
        '性能优化': jsContent.includes('will-change'),
        '错误处理': jsContent.includes('catch')
    };
    
    for (const [check, passed] of Object.entries(jsChecks)) {
        verificationResults.functionality[check] = passed;
        console.log(`  ${passed ? '✅' : '❌'} ${check}`);
    }
}

// 4. 集成验证
console.log('\n🔗 4. 集成验证');
const mainCssPath = path.join(__dirname, '..', 'src/client/styles/main.css');
if (fs.existsSync(mainCssPath)) {
    const mainCssContent = fs.readFileSync(mainCssPath, 'utf8');
    
    const integrationChecks = {
        '主样式文件集成': mainCssContent.includes('starry-background.css'),
        'import语句正确': mainCssContent.includes("@import url('./starry-background.css')")
    };
    
    for (const [check, passed] of Object.entries(integrationChecks)) {
        verificationResults.integration[check] = passed;
        console.log(`  ${passed ? '✅' : '❌'} ${check}`);
    }
}

// 5. 测试页面验证
console.log('\n🧪 5. 测试页面验证');
const testHtmlPath = path.join(__dirname, 'test-starry-background.html');
if (fs.existsSync(testHtmlPath)) {
    const testHtmlContent = fs.readFileSync(testHtmlPath, 'utf8');
    
    const testChecks = {
        '引入星空背景CSS': testHtmlContent.includes('starry-background.css'),
        '引入智能加载器': testHtmlContent.includes('video-loader.js'),
        '页面主题测试': testHtmlContent.includes('testPageTheme'),
        '故障模拟测试': testHtmlContent.includes('simulateFailure'),
        '性能测试': testHtmlContent.includes('performanceTest'),
        '日志功能': testHtmlContent.includes('log-panel')
    };
    
    for (const [check, passed] of Object.entries(testChecks)) {
        verificationResults.integration[check] = passed;
        console.log(`  ${passed ? '✅' : '❌'} ${check}`);
    }
}

// 6. 文档验证
console.log('\n📖 6. 文档验证');
const docPath = path.join(__dirname, '..', 'docs/starry-background-guide.md');
if (fs.existsSync(docPath)) {
    const docContent = fs.readFileSync(docPath, 'utf8');
    
    const docChecks = {
        '架构设计说明': docContent.includes('四层降级架构'),
        '使用方法说明': docContent.includes('使用方法'),
        '主题配置说明': docContent.includes('页面主题配置'),
        '性能优化说明': docContent.includes('性能优化'),
        '测试调试说明': docContent.includes('测试和调试'),
        '维护更新说明': docContent.includes('维护和更新')
    };
    
    for (const [check, passed] of Object.entries(docChecks)) {
        verificationResults.integration[check] = passed;
        console.log(`  ${passed ? '✅' : '❌'} ${check}`);
    }
}

// 7. 性能评估
console.log('\n⚡ 7. 性能评估');
if (fs.existsSync(cssPath)) {
    const cssContent = fs.readFileSync(cssPath, 'utf8');
    const cssSize = Buffer.byteLength(cssContent, 'utf8');
    
    const performanceChecks = {
        'CSS文件大小合理': cssSize < 10240, // 小于10KB
        'GPU加速优化': cssContent.includes('transform: translateZ(0)'),
        '动画性能优化': cssContent.includes('will-change'),
        '减少动画偏好': cssContent.includes('prefers-reduced-motion'),
        '响应式优化': cssContent.includes('@media')
    };
    
    for (const [check, passed] of Object.entries(performanceChecks)) {
        verificationResults.performance[check] = passed;
        console.log(`  ${passed ? '✅' : '❌'} ${check}`);
    }
    
    console.log(`  📊 CSS文件大小: ${(cssSize / 1024).toFixed(2)}KB`);
}

// 8. 生成验证报告
console.log('\n📋 8. 验证报告生成');

const totalChecks = Object.values(verificationResults).reduce((total, category) => {
    return total + Object.keys(category).length;
}, 0);

const passedChecks = Object.values(verificationResults).reduce((total, category) => {
    return total + Object.values(category).filter(Boolean).length;
}, 0);

const successRate = ((passedChecks / totalChecks) * 100).toFixed(1);

console.log(`\n🎯 验证结果总结:`);
console.log(`  总检查项: ${totalChecks}`);
console.log(`  通过项: ${passedChecks}`);
console.log(`  成功率: ${successRate}%`);

if (successRate >= 90) {
    console.log(`  🎉 验证结果: 优秀 - 星空背景保障机制完全就绪！`);
} else if (successRate >= 80) {
    console.log(`  ✅ 验证结果: 良好 - 星空背景保障机制基本就绪`);
} else if (successRate >= 70) {
    console.log(`  ⚠️ 验证结果: 一般 - 需要修复部分问题`);
} else {
    console.log(`  ❌ 验证结果: 不合格 - 需要重大修复`);
}

// 保存验证结果
const reportPath = path.join(__dirname, 'starry-background-verification-report.json');
const report = {
    timestamp: new Date().toISOString(),
    totalChecks,
    passedChecks,
    successRate: parseFloat(successRate),
    results: verificationResults,
    summary: {
        status: successRate >= 90 ? 'excellent' : successRate >= 80 ? 'good' : successRate >= 70 ? 'fair' : 'poor',
        recommendation: successRate >= 90 ? '可以投入生产使用' : successRate >= 80 ? '建议修复小问题后使用' : '需要修复问题后再使用'
    }
};

fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
console.log(`\n📄 详细报告已保存: ${reportPath}`);

console.log('\n🌟 星空背景保障机制验证完成！');

// 返回验证结果
process.exit(successRate >= 80 ? 0 : 1);
