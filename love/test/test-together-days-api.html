<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Together Days API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }
        h1, h2 {
            text-align: center;
            margin-bottom: 30px;
        }
        .api-section {
            margin-bottom: 40px;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            background: linear-gradient(45deg, #48cae4, #0077b6);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(72, 202, 228, 0.4);
        }
        .result {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left: 4px solid #f44336;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <h1>💕 Together Days API 测试页面</h1>
    
    <!-- 时光轴 API 测试 -->
    <div class="container">
        <h2>🕐 时光轴 API 测试</h2>
        <div class="api-section">
            <div class="test-buttons">
                <button onclick="testTimelineGet()">获取时光轴</button>
                <button onclick="testTimelinePost()">创建时光轴</button>
                <button onclick="testTimelineUpdate()">更新时光轴</button>
                <button onclick="testTimelineDelete()">删除时光轴</button>
            </div>
            
            <div class="form-group">
                <label>创建/更新时光轴数据：</label>
                <input type="text" id="timeline-date" placeholder="日期 (如: 2024年7月25日)" value="2024年7月25日">
                <input type="text" id="timeline-title" placeholder="标题 (如: 🎉 测试时刻)" value="🎉 API测试时刻">
                <textarea id="timeline-description" placeholder="描述内容">这是一个API测试创建的时光轴记录，用于验证数据库功能是否正常工作。</textarea>
                <input type="number" id="timeline-sort" placeholder="排序 (数字)" value="999">
                <input type="number" id="timeline-id" placeholder="ID (仅更新/删除时需要)" value="">
            </div>
            
            <div id="timeline-result" class="result"></div>
        </div>
    </div>

    <!-- 美好瞬间 API 测试 -->
    <div class="container">
        <h2>✨ 美好瞬间 API 测试</h2>
        <div class="api-section">
            <div class="test-buttons">
                <button onclick="testMemoriesGet()">获取美好瞬间</button>
                <button onclick="testMemoriesPost()">创建美好瞬间</button>
                <button onclick="testMemoriesUpdate()">更新美好瞬间</button>
                <button onclick="testMemoriesDelete()">删除美好瞬间</button>
            </div>
            
            <div class="form-group">
                <label>创建/更新美好瞬间数据：</label>
                <input type="text" id="memory-icon" placeholder="图标类名 (如: fas fa-heart)" value="fas fa-code">
                <input type="text" id="memory-title" placeholder="标题 (如: 编程时光)" value="API测试瞬间">
                <textarea id="memory-content" placeholder="内容描述">这是一个通过API测试创建的美好瞬间记录，记录了开发数据库功能的美好时光。</textarea>
                <input type="number" id="memory-sort" placeholder="排序 (数字)" value="999">
                <input type="number" id="memory-id" placeholder="ID (仅更新/删除时需要)" value="">
            </div>
            
            <div id="memories-result" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = '/love/api';
        
        // 显示结果的通用函数
        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
        }
        
        // 时光轴 API 测试函数
        async function testTimelineGet() {
            try {
                const response = await fetch(`${API_BASE}/timeline`);
                const data = await response.json();
                showResult('timeline-result', data, data.success);
            } catch (error) {
                showResult('timeline-result', { error: error.message }, false);
            }
        }
        
        async function testTimelinePost() {
            try {
                const data = {
                    date: document.getElementById('timeline-date').value,
                    title: document.getElementById('timeline-title').value,
                    description: document.getElementById('timeline-description').value,
                    sort_order: parseInt(document.getElementById('timeline-sort').value) || 0
                };
                
                const response = await fetch(`${API_BASE}/timeline`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                showResult('timeline-result', result, result.success);
            } catch (error) {
                showResult('timeline-result', { error: error.message }, false);
            }
        }
        
        async function testTimelineUpdate() {
            const id = document.getElementById('timeline-id').value;
            if (!id) {
                showResult('timeline-result', { error: '请输入要更新的时光轴ID' }, false);
                return;
            }
            
            try {
                const data = {
                    date: document.getElementById('timeline-date').value,
                    title: document.getElementById('timeline-title').value,
                    description: document.getElementById('timeline-description').value,
                    sort_order: parseInt(document.getElementById('timeline-sort').value) || 0
                };
                
                const response = await fetch(`${API_BASE}/timeline/${id}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                showResult('timeline-result', result, result.success);
            } catch (error) {
                showResult('timeline-result', { error: error.message }, false);
            }
        }
        
        async function testTimelineDelete() {
            const id = document.getElementById('timeline-id').value;
            if (!id) {
                showResult('timeline-result', { error: '请输入要删除的时光轴ID' }, false);
                return;
            }
            
            if (!confirm(`确定要删除ID为 ${id} 的时光轴记录吗？`)) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/timeline/${id}`, {
                    method: 'DELETE'
                });
                const result = await response.json();
                showResult('timeline-result', result, result.success);
            } catch (error) {
                showResult('timeline-result', { error: error.message }, false);
            }
        }
        
        // 美好瞬间 API 测试函数
        async function testMemoriesGet() {
            try {
                const response = await fetch(`${API_BASE}/memories`);
                const data = await response.json();
                showResult('memories-result', data, data.success);
            } catch (error) {
                showResult('memories-result', { error: error.message }, false);
            }
        }
        
        async function testMemoriesPost() {
            try {
                const data = {
                    icon: document.getElementById('memory-icon').value,
                    title: document.getElementById('memory-title').value,
                    content: document.getElementById('memory-content').value,
                    sort_order: parseInt(document.getElementById('memory-sort').value) || 0
                };
                
                const response = await fetch(`${API_BASE}/memories`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                showResult('memories-result', result, result.success);
            } catch (error) {
                showResult('memories-result', { error: error.message }, false);
            }
        }
        
        async function testMemoriesUpdate() {
            const id = document.getElementById('memory-id').value;
            if (!id) {
                showResult('memories-result', { error: '请输入要更新的美好瞬间ID' }, false);
                return;
            }
            
            try {
                const data = {
                    icon: document.getElementById('memory-icon').value,
                    title: document.getElementById('memory-title').value,
                    content: document.getElementById('memory-content').value,
                    sort_order: parseInt(document.getElementById('memory-sort').value) || 0
                };
                
                const response = await fetch(`${API_BASE}/memories/${id}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                showResult('memories-result', result, result.success);
            } catch (error) {
                showResult('memories-result', { error: error.message }, false);
            }
        }
        
        async function testMemoriesDelete() {
            const id = document.getElementById('memory-id').value;
            if (!id) {
                showResult('memories-result', { error: '请输入要删除的美好瞬间ID' }, false);
                return;
            }
            
            if (!confirm(`确定要删除ID为 ${id} 的美好瞬间记录吗？`)) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/memories/${id}`, {
                    method: 'DELETE'
                });
                const result = await response.json();
                showResult('memories-result', result, result.success);
            } catch (error) {
                showResult('memories-result', { error: error.message }, false);
            }
        }
        
        // 页面加载时自动获取数据
        window.onload = function() {
            testTimelineGet();
            testMemoriesGet();
        };
    </script>
</body>
</html>
