<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星空背景调试页面 - Love Project</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #000;
            color: white;
        }
        .debug-panel {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
        }
        .status {
            padding: 5px 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .status.success { background: rgba(0,255,0,0.2); }
        .status.error { background: rgba(255,0,0,0.2); }
        .status.info { background: rgba(0,100,255,0.2); }
        .test-button {
            padding: 10px 20px;
            margin: 5px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #5a6fd8;
        }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }
        .video-background video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <!-- 视频背景容器 -->
    <div class="video-background" id="videoBackground">
        <video id="testVideo" autoplay muted loop playsinline>
            <source src="" type="video/mp4">
        </video>
    </div>

    <h1>🌟 星空背景调试页面</h1>
    
    <div class="debug-panel">
        <h2>📊 系统状态检查</h2>
        <div id="system-status"></div>
    </div>
    
    <div class="debug-panel">
        <h2>🧪 功能测试</h2>
        <button class="test-button" onclick="testVideoLoader()">测试视频加载器</button>
        <button class="test-button" onclick="testStarryBackground()">测试星空背景</button>
        <button class="test-button" onclick="testThemeSwitch()">测试主题切换</button>
        <button class="test-button" onclick="clearConsole()">清空控制台</button>
    </div>
    
    <div class="debug-panel">
        <h2>📝 控制台输出</h2>
        <div id="console-output"></div>
    </div>

    <script>
        // 控制台输出重定向
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const line = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            consoleOutput.textContent += line;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        // 系统状态检查
        function checkSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            const checks = [];
            
            // 检查DOM元素
            checks.push({
                name: 'testVideo元素',
                status: !!document.getElementById('testVideo'),
                type: 'dom'
            });
            
            checks.push({
                name: 'videoBackground元素',
                status: !!document.getElementById('videoBackground'),
                type: 'dom'
            });
            
            // 检查全局对象
            checks.push({
                name: 'window.VideoLoader',
                status: !!window.VideoLoader,
                type: 'global'
            });
            
            checks.push({
                name: 'window.videoLoader',
                status: !!window.videoLoader,
                type: 'global'
            });
            
            // 检查方法
            if (window.videoLoader) {
                checks.push({
                    name: 'videoLoader.init方法',
                    status: typeof window.videoLoader.init === 'function',
                    type: 'method'
                });
                
                checks.push({
                    name: 'videoLoader.loadStarryBackground方法',
                    status: typeof window.videoLoader.loadStarryBackground === 'function',
                    type: 'method'
                });
            }
            
            // 渲染状态
            statusDiv.innerHTML = checks.map(check => 
                `<div class="status ${check.status ? 'success' : 'error'}">
                    ${check.status ? '✅' : '❌'} ${check.name}: ${check.status}
                </div>`
            ).join('');
            
            console.log('系统状态检查完成');
            return checks.every(check => check.status);
        }
        
        // 测试视频加载器
        async function testVideoLoader() {
            console.log('🧪 开始测试视频加载器...');
            
            try {
                if (!window.videoLoader) {
                    throw new Error('videoLoader未定义');
                }
                
                console.log('✅ videoLoader对象存在');
                
                // 测试初始化
                await window.videoLoader.init();
                console.log('✅ videoLoader初始化成功');
                
                // 获取配置
                const config = window.videoLoader.getConfig();
                console.log('✅ 获取配置成功:', JSON.stringify(config, null, 2));
                
            } catch (error) {
                console.error('❌ 视频加载器测试失败:', error.message);
            }
        }
        
        // 测试星空背景
        async function testStarryBackground() {
            console.log('🌟 开始测试星空背景...');
            
            try {
                const videoElement = document.getElementById('testVideo');
                const videoContainer = document.getElementById('videoBackground');
                
                if (!videoElement || !videoContainer) {
                    throw new Error('视频元素未找到');
                }
                
                console.log('✅ 视频元素存在');
                
                // 测试星空背景加载
                const success = await window.videoLoader.loadStarryBackground(
                    'home',
                    videoElement,
                    {
                        onSuccess: (layer) => {
                            console.log(`✅ 星空背景加载成功: ${layer}`);
                        },
                        onError: (error) => {
                            console.error(`❌ 星空背景加载错误: ${error.message}`);
                        }
                    }
                );
                
                if (success) {
                    console.log('✅ 星空背景测试成功');
                } else {
                    console.error('❌ 星空背景测试失败');
                }
                
            } catch (error) {
                console.error('❌ 星空背景测试异常:', error.message);
            }
        }
        
        // 测试主题切换
        async function testThemeSwitch() {
            console.log('🎨 开始测试主题切换...');
            
            const themes = ['home', 'anniversary', 'meetings', 'memorial', 'together-days'];
            const videoElement = document.getElementById('testVideo');
            
            for (const theme of themes) {
                try {
                    console.log(`测试${theme}主题...`);
                    
                    const success = await window.videoLoader.loadStarryBackground(
                        theme,
                        videoElement
                    );
                    
                    console.log(`${success ? '✅' : '❌'} ${theme}主题: ${success}`);
                    
                    // 等待一下再测试下一个主题
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                } catch (error) {
                    console.error(`❌ ${theme}主题测试失败:`, error.message);
                }
            }
            
            console.log('🎨 主题切换测试完成');
        }
        
        // 清空控制台
        function clearConsole() {
            consoleOutput.textContent = '';
            console.log('控制台已清空');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('📄 DOM加载完成');
            
            // 等待一下让其他脚本加载
            setTimeout(() => {
                console.log('🔍 开始系统状态检查...');
                const allGood = checkSystemStatus();
                
                if (allGood) {
                    console.log('✅ 所有系统检查通过');
                } else {
                    console.warn('⚠️ 部分系统检查失败');
                }
            }, 1000);
        });
        
        // 监听错误
        window.addEventListener('error', (event) => {
            console.error('页面错误:', event.error?.message || event.message);
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的Promise拒绝:', event.reason);
        });
    </script>
    
    <!-- 引入智能视频加载器 -->
    <script src="/src/client/scripts/video-loader.js"></script>
</body>
</html>
