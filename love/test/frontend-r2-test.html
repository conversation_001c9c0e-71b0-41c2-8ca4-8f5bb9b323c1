<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>R2前端加载测试 - Love Website</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .video-test {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        .video-container {
            position: relative;
            width: 100%;
            height: 200px;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .video-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
        }
        
        .loading-overlay.hidden {
            display: none;
        }
        
        .progress-bar {
            width: 80%;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 10px;
        }
        
        .progress-fill {
            height: 100%;
            background: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .test-info {
            font-size: 14px;
        }
        
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #fff;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }
        
        .status.loading {
            background: #ff9800;
            color: white;
        }
        
        .status.success {
            background: #4CAF50;
            color: white;
        }
        
        .status.error {
            background: #f44336;
            color: white;
        }
        
        .summary {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            backdrop-filter: blur(10px);
            text-align: center;
        }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .metric {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .metric-label {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 视频加载并发问题诊断测试</h1>
            <p>测试策略: 串行加载 + 独立实例 | 解决并发冲突问题</p>
            <div style="margin-top: 15px; margin-bottom: 20px;">
                <button onclick="startTest()" style="padding: 15px 30px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; font-weight: bold;">🚀 开始测试</button>
                <button onclick="resetTest()" style="padding: 15px 30px; background: #f44336; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 15px; font-size: 16px; font-weight: bold;">🔄 重置测试</button>
                <button onclick="testSingleVideo()" style="padding: 15px 30px; background: #ff9800; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 15px; font-size: 16px; font-weight: bold;">🎯 测试单个</button>
            </div>
            <div id="testStatus" style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                <strong>测试状态：</strong><span id="currentStatus">等待开始</span>
            </div>
        </div>
        
        <div class="test-grid" id="testGrid">
            <!-- 测试项目将通过JavaScript动态生成 -->
        </div>
        
        <div class="summary" id="summary">
            <h2>📊 测试总结</h2>
            <div id="summaryContent">
                <p>等待测试开始...</p>
            </div>
            <div class="metrics" id="metrics">
                <!-- 指标将动态生成 -->
            </div>
        </div>
    </div>

    <!-- 引入配置和智能加载器 -->
    <script src="../src/client/scripts/config.js"></script>
    <script src="../src/client/scripts/video-loader.js"></script>
    
    <script>
        class FrontendR2Test {
            constructor() {
                this.videos = [
                    { name: 'home', title: '首页视频' },
                    { name: 'anniversary', title: '纪念日视频' },
                    { name: 'meetings', title: '相遇记录视频' },
                    { name: 'memorial', title: '纪念页面视频' },
                    { name: 'together-days', title: '在一起的日子视频' }
                ];
                
                this.results = {
                    total: this.videos.length,
                    success: 0,
                    failed: 0,
                    loadTimes: [],
                    errors: []
                };
                
                this.startTime = Date.now();
            }
            
            init() {
                console.log('🚀 初始化视频加载并发问题诊断测试');
                console.log('当前配置:', window.CONFIG);

                this.createTestElements();
                // 不自动开始测试，等待用户点击
            }

            async startTest() {
                console.log('🎬 用户手动开始测试');
                this.updateStatus('开始串行测试...');
                this.resetResults();
                await this.startTests();
            }

            resetTest() {
                console.log('🔄 重置测试');
                this.updateStatus('测试已重置');
                this.resetResults();
                this.updateAllStatus('等待测试', 'loading');
            }

            async testSingleVideo() {
                console.log('🎯 测试单个视频 - home');
                this.updateStatus('测试单个视频: home');
                this.resetResults();

                try {
                    await this.testVideoSerial(this.videos[0], 1);
                    console.log('✅ 单个视频测试完成');
                    this.updateStatus('单个视频测试完成');
                } catch (error) {
                    console.error('❌ 单个视频测试失败:', error);
                    this.updateStatus('单个视频测试失败: ' + error.message);
                }
            }

            updateStatus(message) {
                const statusElement = document.getElementById('currentStatus');
                if (statusElement) {
                    statusElement.textContent = message;
                }
                console.log('📊 状态更新:', message);
            }

            resetResults() {
                this.results = {
                    total: this.videos.length,
                    success: 0,
                    failed: 0,
                    loadTimes: [],
                    errors: []
                };
                this.startTime = Date.now();
            }
            
            createTestElements() {
                const testGrid = document.getElementById('testGrid');
                
                this.videos.forEach(video => {
                    const testElement = document.createElement('div');
                    testElement.className = 'video-test';
                    testElement.innerHTML = `
                        <div class="video-container">
                            <video id="video-${video.name}" muted loop playsinline preload="metadata">
                                <source src="/src/client/assets/video-compressed/${video.name}.mp4" type="video/mp4">
                            </video>
                            <div class="loading-overlay" id="loading-${video.name}">
                                <div>加载中...</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="progress-${video.name}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="test-info">
                            <h3>${video.title}</h3>
                            <div class="status loading" id="status-${video.name}">等待测试</div>
                            <div id="info-${video.name}">
                                <div>文件: ${video.name}.mp4</div>
                                <div>状态: 准备中</div>
                                <div>加载时间: --</div>
                            </div>
                        </div>
                    `;
                    
                    testGrid.appendChild(testElement);
                });
            }
            
            async startTests() {
                console.log('📋 开始串行测试视频加载 - 解决并发冲突问题');
                this.updateStatus('开始串行测试...');

                for (let i = 0; i < this.videos.length; i++) {
                    const video = this.videos[i];
                    console.log(`\n🎬 [${i + 1}/${this.videos.length}] 开始测试: ${video.name}`);
                    this.updateStatus(`正在测试 ${i + 1}/${this.videos.length}: ${video.title}`);

                    try {
                        await this.testVideoSerial(video, i + 1);
                        console.log(`✅ [${i + 1}/${this.videos.length}] 完成测试: ${video.name}`);
                        this.updateStatus(`完成测试 ${i + 1}/${this.videos.length}: ${video.title}`);
                    } catch (error) {
                        console.error(`❌ [${i + 1}/${this.videos.length}] 测试异常: ${video.name}`, error);
                        this.updateStatus(`测试失败 ${i + 1}/${this.videos.length}: ${video.title} - ${error.message}`);
                    }

                    // 测试间隔，避免请求过于密集
                    if (i < this.videos.length - 1) {
                        console.log(`⏳ 等待 1.5 秒后继续下一个测试...`);
                        this.updateStatus(`等待 1.5 秒后继续下一个测试...`);
                        await this.delay(1500);
                    }
                }

                console.log('\n📊 所有测试完成，生成总结...');
                this.updateStatus('所有测试完成，生成总结...');
                this.generateSummary();
            }
            
            async testVideoSerial(video, index) {
                console.log(`🎯 串行测试视频: ${video.name}`);

                const videoElement = document.getElementById(`video-${video.name}`);
                const loadingElement = document.getElementById(`loading-${video.name}`);
                const progressElement = document.getElementById(`progress-${video.name}`);
                const statusElement = document.getElementById(`status-${video.name}`);
                const infoElement = document.getElementById(`info-${video.name}`);

                // 更新状态
                statusElement.textContent = '加载中';
                statusElement.className = 'status loading';
                loadingElement.classList.remove('hidden');

                const startTime = Date.now();

                try {
                    // 创建独立的VideoLoader实例，避免全局状态冲突
                    const loader = new VideoLoader();

                    console.log(`🔄 [${index}] 使用独立加载器实例加载 ${video.name}`);

                    // 直接await loadVideo的结果
                    const success = await loader.loadVideo(video.name, videoElement, {
                        onProgress: (progress, layerInfo) => {
                            progressElement.style.width = Math.min(progress, 95) + '%';
                            console.log(`📊 [${index}] ${video.name} ${layerInfo?.key || 'unknown'} 进度: ${progress.toFixed(1)}%`);
                        },
                        onError: (error, layerKey) => {
                            console.warn(`⚠️ [${index}] ${video.name} ${layerKey} 层失败: ${error.message}`);
                        },
                        onSuccess: (layerKey) => {
                            console.log(`🎯 [${index}] ${video.name} ${layerKey} 层加载成功回调`);
                        }
                    });

                    const endTime = Date.now();
                    const totalTime = endTime - startTime;

                    if (success) {
                        progressElement.style.width = '100%';

                        // 隐藏加载遮罩
                        setTimeout(() => {
                            loadingElement.classList.add('hidden');
                        }, 300);

                        // 更新状态
                        statusElement.textContent = '加载成功';
                        statusElement.className = 'status success';

                        // 检查视频元素状态来确定来源
                        let sourceType = '未知';
                        if (videoElement.style.display === 'none') {
                            sourceType = '星空背景';
                        } else if (videoElement.src && videoElement.src.includes('r2.dev')) {
                            sourceType = 'R2 CDN';
                        } else if (videoElement.src && videoElement.src.includes('cloudinary')) {
                            sourceType = 'Cloudinary';
                        } else if (videoElement.src) {
                            sourceType = 'VPS';
                        }

                        infoElement.innerHTML = `
                            <div>文件: ${video.name}.mp4</div>
                            <div>状态: ✅ 成功</div>
                            <div>加载时间: ${totalTime}ms</div>
                            <div>来源: ${sourceType}</div>
                        `;

                        this.results.success++;
                        this.results.loadTimes.push(totalTime);

                        console.log(`✅ [${index}] ${video.name} 加载成功: ${totalTime}ms (${sourceType})`);

                        // 如果是视频成功加载，尝试播放
                        if (videoElement.style.display !== 'none' && videoElement.src) {
                            videoElement.play().catch(error => {
                                console.log(`📱 [${index}] ${video.name} 自动播放被阻止:`, error);
                            });
                        }
                    } else {
                        throw new Error('视频加载失败');
                    }

                } catch (error) {
                    const endTime = Date.now();
                    const totalTime = endTime - startTime;

                    // 更新状态
                    statusElement.textContent = '加载失败';
                    statusElement.className = 'status error';

                    infoElement.innerHTML = `
                        <div>文件: ${video.name}.mp4</div>
                        <div>状态: ❌ 失败</div>
                        <div>加载时间: ${totalTime}ms</div>
                        <div>错误: ${error.message || error}</div>
                    `;

                    this.results.failed++;
                    this.results.errors.push({ video: video.name, error: error.message || error });

                    console.error(`❌ [${index}] ${video.name} 加载失败:`, error);
                }
            }

            updateAllStatus(text, className) {
                this.videos.forEach(video => {
                    const statusElement = document.getElementById(`status-${video.name}`);
                    const loadingElement = document.getElementById(`loading-${video.name}`);
                    const progressElement = document.getElementById(`progress-${video.name}`);

                    if (statusElement) {
                        statusElement.textContent = text;
                        statusElement.className = `status ${className}`;
                    }
                    if (loadingElement) {
                        loadingElement.classList.remove('hidden');
                    }
                    if (progressElement) {
                        progressElement.style.width = '0%';
                    }
                });
            }



            
            generateSummary() {
                const totalTime = Date.now() - this.startTime;
                const successRate = (this.results.success / this.results.total * 100).toFixed(1);
                const avgLoadTime = this.results.loadTimes.length > 0 
                    ? Math.round(this.results.loadTimes.reduce((a, b) => a + b, 0) / this.results.loadTimes.length)
                    : 0;
                
                const summaryContent = document.getElementById('summaryContent');
                summaryContent.innerHTML = `
                    <p><strong>测试完成！</strong></p>
                    <p>成功率: ${successRate}% (${this.results.success}/${this.results.total})</p>
                    <p>总用时: ${totalTime}ms</p>
                `;
                
                const metrics = document.getElementById('metrics');
                metrics.innerHTML = `
                    <div class="metric">
                        <div class="metric-value">${this.results.success}</div>
                        <div class="metric-label">成功加载</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">${this.results.failed}</div>
                        <div class="metric-label">加载失败</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">${avgLoadTime}ms</div>
                        <div class="metric-label">平均加载时间</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">${successRate}%</div>
                        <div class="metric-label">成功率</div>
                    </div>
                `;
                
                console.log('📊 测试总结:', this.results);
                
                if (this.results.failed === 0) {
                    console.log('🎉 所有视频加载成功！R2存储桶测试通过！');
                } else {
                    console.log('⚠️ 部分视频加载失败，需要检查问题');
                }
            }
            
            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // 全局变量
        let testInstance = null;

        // 全局函数
        function startTest() {
            console.log('🎯 全局函数 startTest 被调用');
            if (testInstance) {
                testInstance.startTest();
            } else {
                console.error('❌ testInstance 未初始化');
            }
        }

        function resetTest() {
            console.log('🔄 全局函数 resetTest 被调用');
            if (testInstance) {
                testInstance.resetTest();
            } else {
                console.error('❌ testInstance 未初始化');
            }
        }

        function testSingleVideo() {
            console.log('🎯 全局函数 testSingleVideo 被调用');
            if (testInstance) {
                testInstance.testSingleVideo();
            } else {
                console.error('❌ testInstance 未初始化');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            testInstance = new FrontendR2Test();
            testInstance.init();
            console.log('🎯 测试页面已准备就绪，点击"开始测试"按钮开始');
        });
    </script>
</body>
</html>
