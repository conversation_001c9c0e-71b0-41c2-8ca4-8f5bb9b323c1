<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>R2前端加载测试 - Love Website</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .video-test {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        .video-container {
            position: relative;
            width: 100%;
            height: 200px;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .video-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
        }
        
        .loading-overlay.hidden {
            display: none;
        }
        
        .progress-bar {
            width: 80%;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 10px;
        }
        
        .progress-fill {
            height: 100%;
            background: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .test-info {
            font-size: 14px;
        }
        
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #fff;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }
        
        .status.loading {
            background: #ff9800;
            color: white;
        }
        
        .status.success {
            background: #4CAF50;
            color: white;
        }
        
        .status.error {
            background: #f44336;
            color: white;
        }
        
        .summary {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            backdrop-filter: blur(10px);
            text-align: center;
        }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .metric {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .metric-label {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 R2存储桶前端加载测试</h1>
            <p>测试策略: R2_ONLY | 验证四层架构第一层性能</p>
        </div>
        
        <div class="test-grid" id="testGrid">
            <!-- 测试项目将通过JavaScript动态生成 -->
        </div>
        
        <div class="summary" id="summary">
            <h2>📊 测试总结</h2>
            <div id="summaryContent">
                <p>等待测试开始...</p>
            </div>
            <div class="metrics" id="metrics">
                <!-- 指标将动态生成 -->
            </div>
        </div>
    </div>

    <!-- 引入配置和智能加载器 -->
    <script src="../src/client/scripts/config.js"></script>
    <script src="../src/client/scripts/video-loader.js"></script>
    
    <script>
        class FrontendR2Test {
            constructor() {
                this.videos = [
                    { name: 'home', title: '首页视频' },
                    { name: 'anniversary', title: '纪念日视频' },
                    { name: 'meetings', title: '相遇记录视频' },
                    { name: 'memorial', title: '纪念页面视频' },
                    { name: 'together-days', title: '在一起的日子视频' }
                ];
                
                this.results = {
                    total: this.videos.length,
                    success: 0,
                    failed: 0,
                    loadTimes: [],
                    errors: []
                };
                
                this.startTime = Date.now();
            }
            
            init() {
                console.log('🚀 开始R2前端加载测试');
                console.log('当前配置:', window.CONFIG);
                
                this.createTestElements();
                this.startTests();
            }
            
            createTestElements() {
                const testGrid = document.getElementById('testGrid');
                
                this.videos.forEach(video => {
                    const testElement = document.createElement('div');
                    testElement.className = 'video-test';
                    testElement.innerHTML = `
                        <div class="video-container">
                            <video id="video-${video.name}" muted loop playsinline preload="metadata">
                                <source src="/src/client/assets/video-compressed/${video.name}.mp4" type="video/mp4">
                            </video>
                            <div class="loading-overlay" id="loading-${video.name}">
                                <div>加载中...</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="progress-${video.name}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="test-info">
                            <h3>${video.title}</h3>
                            <div class="status loading" id="status-${video.name}">等待测试</div>
                            <div id="info-${video.name}">
                                <div>文件: ${video.name}.mp4</div>
                                <div>状态: 准备中</div>
                                <div>加载时间: --</div>
                            </div>
                        </div>
                    `;
                    
                    testGrid.appendChild(testElement);
                });
            }
            
            async startTests() {
                console.log('📋 开始串行测试视频加载...');
                console.log('🔧 修复方案: 独立加载器实例 + 真正串行执行');

                // 重置VideoLoader全局状态
                if (window.videoLoader) {
                    window.videoLoader.reset();
                }

                for (let i = 0; i < this.videos.length; i++) {
                    const video = this.videos[i];
                    console.log(`\n🎬 [${i + 1}/${this.videos.length}] 开始测试: ${video.name}`);

                    try {
                        await this.testVideo(video);
                        console.log(`✅ [${i + 1}/${this.videos.length}] 完成测试: ${video.name}`);
                    } catch (error) {
                        console.error(`❌ [${i + 1}/${this.videos.length}] 测试异常: ${video.name}`, error);
                    }

                    // 增加测试间隔，避免请求过于密集
                    if (i < this.videos.length - 1) {
                        console.log(`⏳ 等待 2 秒后继续下一个测试...`);
                        await this.delay(2000);
                    }
                }

                console.log('\n📊 所有测试完成，生成总结...');
                this.generateSummary();
            }
            
            async testVideo(video) {
                console.log(`🎬 测试视频: ${video.name}`);

                const videoElement = document.getElementById(`video-${video.name}`);
                const loadingElement = document.getElementById(`loading-${video.name}`);
                const progressElement = document.getElementById(`progress-${video.name}`);
                const statusElement = document.getElementById(`status-${video.name}`);
                const infoElement = document.getElementById(`info-${video.name}`);

                // 更新状态
                statusElement.textContent = '加载中';
                statusElement.className = 'status loading';

                const startTime = Date.now();
                let progressInterval;
                let currentProgress = 0;

                return new Promise((resolve) => {
                    // 模拟进度
                    progressInterval = setInterval(() => {
                        if (currentProgress < 90) {
                            currentProgress += Math.random() * 10;
                            if (currentProgress > 90) currentProgress = 90;
                            progressElement.style.width = currentProgress + '%';
                        }
                    }, 100);

                    // 使用智能加载器 - 修复并发问题
                    if (typeof VideoLoader !== 'undefined') {
                        console.log(`🔄 使用智能加载器串行加载 ${video.name}`);

                        // 创建独立的加载器实例避免状态冲突
                        const loader = new VideoLoader();

                        // 直接调用loadVideo方法而不是integrateWithPage
                        loader.loadVideo(video.name, videoElement, {
                            onProgress: (progress, layerInfo) => {
                                if (progress > currentProgress) {
                                    currentProgress = progress;
                                    progressElement.style.width = Math.min(progress, 95) + '%';
                                }
                                console.log(`📊 ${video.name} ${layerInfo?.key || 'unknown'} 进度: ${progress.toFixed(1)}%`);
                            },
                            onError: (error, layerKey) => {
                                console.warn(`⚠️ ${video.name} ${layerKey} 层失败: ${error.message}`);
                            },
                            onSuccess: (layerKey) => {
                                const endTime = Date.now();
                                const totalTime = endTime - startTime;

                                clearInterval(progressInterval);
                                progressElement.style.width = '100%';

                                // 隐藏加载遮罩
                                setTimeout(() => {
                                    loadingElement.classList.add('hidden');
                                }, 300);

                                // 更新状态
                                statusElement.textContent = '加载成功';
                                statusElement.className = 'status success';

                                const sourceType = layerKey === 'quaternary' ? '星空背景' :
                                                 layerKey === 'primary' ? 'R2 CDN' :
                                                 layerKey === 'secondary' ? 'Cloudinary' :
                                                 layerKey === 'tertiary' ? 'VPS' : '未知';

                                infoElement.innerHTML = `
                                    <div>文件: ${video.name}.mp4</div>
                                    <div>状态: ✅ 成功</div>
                                    <div>加载时间: ${totalTime}ms</div>
                                    <div>来源: ${sourceType} (${layerKey})</div>
                                `;

                                this.results.success++;
                                this.results.loadTimes.push(totalTime);

                                console.log(`✅ ${video.name} 加载成功: ${totalTime}ms (${sourceType})`);

                                // 如果是视频成功加载，尝试播放
                                if (layerKey !== 'quaternary' && videoElement.style.display !== 'none') {
                                    videoElement.play().catch(error => {
                                        console.log(`📱 ${video.name} 自动播放被阻止:`, error);
                                    });
                                }

                                resolve();
                            }
                        }).then(() => {
                            // loadVideo完成后的额外处理
                            console.log(`🎯 ${video.name} loadVideo方法完成`);
                        }).catch((error) => {
                            const endTime = Date.now();
                            const totalTime = endTime - startTime;

                            clearInterval(progressInterval);

                            // 更新状态
                            statusElement.textContent = '加载失败';
                            statusElement.className = 'status error';

                            infoElement.innerHTML = `
                                <div>文件: ${video.name}.mp4</div>
                                <div>状态: ❌ 失败</div>
                                <div>加载时间: ${totalTime}ms</div>
                                <div>错误: ${error.message || error}</div>
                            `;

                            this.results.failed++;
                            this.results.errors.push({ video: video.name, error: error.message || error });

                            console.error(`❌ ${video.name} 加载失败:`, error);
                            resolve();
                        });
                    } else {
                        // 降级到原生加载
                        console.log(`使用原生方法加载 ${video.name}`);
                        
                        videoElement.addEventListener('canplay', () => {
                            const endTime = Date.now();
                            const totalTime = endTime - startTime;
                            
                            clearInterval(progressInterval);
                            progressElement.style.width = '100%';
                            loadingElement.classList.add('hidden');
                            
                            statusElement.textContent = '加载成功';
                            statusElement.className = 'status success';
                            
                            infoElement.innerHTML = `
                                <div>文件: ${video.name}.mp4</div>
                                <div>状态: ✅ 成功 (原生)</div>
                                <div>加载时间: ${totalTime}ms</div>
                            `;
                            
                            this.results.success++;
                            this.results.loadTimes.push(totalTime);
                            
                            videoElement.play().catch(console.error);
                            resolve();
                        });
                        
                        videoElement.addEventListener('error', () => {
                            const endTime = Date.now();
                            const totalTime = endTime - startTime;
                            
                            clearInterval(progressInterval);
                            
                            statusElement.textContent = '加载失败';
                            statusElement.className = 'status error';
                            
                            infoElement.innerHTML = `
                                <div>文件: ${video.name}.mp4</div>
                                <div>状态: ❌ 失败 (原生)</div>
                                <div>加载时间: ${totalTime}ms</div>
                            `;
                            
                            this.results.failed++;
                            this.results.errors.push({ video: video.name, error: 'Video load error' });
                            
                            resolve();
                        });
                        
                        // 超时处理
                        setTimeout(() => {
                            if (statusElement.textContent === '加载中') {
                                clearInterval(progressInterval);
                                
                                statusElement.textContent = '加载超时';
                                statusElement.className = 'status error';
                                
                                this.results.failed++;
                                this.results.errors.push({ video: video.name, error: 'Timeout' });
                                
                                resolve();
                            }
                        }, 10000);
                    }
                });
            }
            
            generateSummary() {
                const totalTime = Date.now() - this.startTime;
                const successRate = (this.results.success / this.results.total * 100).toFixed(1);
                const avgLoadTime = this.results.loadTimes.length > 0 
                    ? Math.round(this.results.loadTimes.reduce((a, b) => a + b, 0) / this.results.loadTimes.length)
                    : 0;
                
                const summaryContent = document.getElementById('summaryContent');
                summaryContent.innerHTML = `
                    <p><strong>测试完成！</strong></p>
                    <p>成功率: ${successRate}% (${this.results.success}/${this.results.total})</p>
                    <p>总用时: ${totalTime}ms</p>
                `;
                
                const metrics = document.getElementById('metrics');
                metrics.innerHTML = `
                    <div class="metric">
                        <div class="metric-value">${this.results.success}</div>
                        <div class="metric-label">成功加载</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">${this.results.failed}</div>
                        <div class="metric-label">加载失败</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">${avgLoadTime}ms</div>
                        <div class="metric-label">平均加载时间</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">${successRate}%</div>
                        <div class="metric-label">成功率</div>
                    </div>
                `;
                
                console.log('📊 测试总结:', this.results);
                
                if (this.results.failed === 0) {
                    console.log('🎉 所有视频加载成功！R2存储桶测试通过！');
                } else {
                    console.log('⚠️ 部分视频加载失败，需要检查问题');
                }
            }
            
            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }
        
        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', () => {
            const test = new FrontendR2Test();
            test.init();
        });
    </script>
</body>
</html>
