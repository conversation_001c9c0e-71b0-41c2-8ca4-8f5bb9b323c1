<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能视频加载器测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Arial', sans-serif;
            background: #1a1a1a;
            color: white;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .video-background {
            position: relative;
            width: 100%;
            height: 400px;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            border: 2px solid #333;
        }
        
        .video-background video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0;
            transition: opacity 1s ease-in-out;
        }
        
        .video-background video.loaded {
            opacity: 1;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transition: opacity 0.5s ease-out;
        }
        
        .loading-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-progress {
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            margin-top: 10px;
            overflow: hidden;
        }
        
        .loading-progress-bar {
            height: 100%;
            background: white;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .test-controls {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .test-controls h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .control-group {
            margin: 15px 0;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        select, button {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            background: #444;
            color: white;
            cursor: pointer;
        }
        
        button {
            background: #4CAF50;
            margin: 5px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .log-container {
            background: #1e1e1e;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 20px 0;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        
        .log-success { color: #4CAF50; }
        .log-warning { color: #FF9800; }
        .log-error { color: #f44336; }
        .log-info { color: #2196F3; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎬 智能视频加载器测试</h1>
        <p>测试四层CDN降级架构：R2 → Cloudinary → VPS → 星空背景</p>
        
        <div class="test-controls">
            <h3>🔧 测试控制</h3>
            
            <div class="control-group">
                <label for="pageSelect">选择页面:</label>
                <select id="pageSelect">
                    <option value="home">首页 (花朵主题)</option>
                    <option value="anniversary">纪念日 (浪漫金色)</option>
                    <option value="meetings">相遇记录 (星河主题)</option>
                    <option value="memorial">纪念页 (海洋主题)</option>
                    <option value="together-days">在一起的日子 (夕阳主题)</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="strategySelect">选择策略:</label>
                <select id="strategySelect">
                    <option value="DEFAULT_LOADING_ORDER">默认策略 (R2→Cloudinary→VPS→星空)</option>
                    <option value="CLOUDINARY_FIRST">Cloudinary优先</option>
                    <option value="VPS_FIRST">VPS优先</option>
                    <option value="R2_ONLY">仅R2测试</option>
                    <option value="CLOUDINARY_ONLY">仅Cloudinary测试</option>
                    <option value="VPS_ONLY">仅VPS测试</option>
                    <option value="STARRY_ONLY">仅星空背景测试</option>
                </select>
            </div>
            
            <div class="control-group">
                <button onclick="startTest()">🚀 开始测试</button>
                <button onclick="clearLog()">🧹 清空日志</button>
                <button onclick="showConfig()">📋 显示配置</button>
            </div>
        </div>
        
        <div class="video-background" id="videoContainer">
            <div class="loading-overlay" id="loadingOverlay">
                <div class="loading-spinner"></div>
                <div class="loading-text">🎬 加载视频中...</div>
                <div class="loading-progress">
                    <div class="loading-progress-bar" id="loadingProgress"></div>
                </div>
            </div>
            <video autoplay muted loop playsinline preload="metadata" id="testVideo">
                <source src="" type="video/mp4">
                您的浏览器不支持视频播放。
            </video>
        </div>
        
        <div class="log-container" id="logContainer">
            <div class="log-entry log-info">📦 等待测试开始...</div>
        </div>
    </div>

    <!-- 引入智能视频加载器 -->
    <script src="/src/client/scripts/video-loader.js"></script>
    
    <script>
        // 日志系统
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // 重写console方法以捕获日志
        const originalConsole = {
            log: console.log,
            warn: console.warn,
            error: console.error
        };
        
        console.log = function(...args) {
            originalConsole.log(...args);
            addLog(args.join(' '), 'info');
        };
        
        console.warn = function(...args) {
            originalConsole.warn(...args);
            addLog(args.join(' '), 'warning');
        };
        
        console.error = function(...args) {
            originalConsole.error(...args);
            addLog(args.join(' '), 'error');
        };
        
        // 测试函数
        async function startTest() {
            const pageName = document.getElementById('pageSelect').value;
            const strategy = document.getElementById('strategySelect').value;
            const videoElement = document.getElementById('testVideo');
            const loadingOverlay = document.getElementById('loadingOverlay');
            const loadingProgress = document.getElementById('loadingProgress');
            
            addLog(`🎬 开始测试: ${pageName} 页面，策略: ${strategy}`, 'info');
            
            // 重置状态
            videoElement.style.display = 'block';
            videoElement.classList.remove('loaded');
            loadingOverlay.classList.remove('hidden');
            loadingProgress.style.width = '0%';
            
            // 重置视频容器背景
            const videoContainer = document.getElementById('videoContainer');
            videoContainer.style.background = '';
            
            try {
                // 设置策略 (模拟)
                if (window.videoLoader) {
                    // 重新初始化以获取新配置
                    window.videoLoader.config = null;
                    await window.videoLoader.init();
                    
                    // 开始加载
                    const success = await window.videoLoader.loadVideo(pageName, videoElement, {
                        onProgress: (progress, layerInfo) => {
                            loadingProgress.style.width = Math.min(progress, 90) + '%';
                            addLog(`📊 ${layerInfo?.key || 'unknown'} 加载进度: ${progress.toFixed(1)}%`, 'info');
                        },
                        onError: (error, layerKey) => {
                            addLog(`⚠️ ${layerKey} 层加载失败: ${error.message}`, 'warning');
                        },
                        onSuccess: (layerKey) => {
                            addLog(`✅ ${layerKey} 层加载成功`, 'success');
                            
                            // 隐藏加载遮罩
                            loadingProgress.style.width = '100%';
                            setTimeout(() => {
                                loadingOverlay.classList.add('hidden');
                            }, 300);

                            // 如果是视频加载成功，设置播放状态
                            if (layerKey !== 'quaternary') {
                                videoElement.classList.add('loaded');
                                videoContainer.classList.add('video-loaded');
                            }
                        }
                    });
                    
                    if (success) {
                        addLog(`🎉 ${pageName} 页面视频加载完成`, 'success');
                        
                        // 如果是视频成功加载，尝试播放
                        if (videoElement.style.display !== 'none') {
                            videoElement.play().catch(error => {
                                addLog(`📱 视频自动播放被阻止: ${error.message}`, 'warning');
                            });
                        }
                    } else {
                        addLog(`❌ ${pageName} 页面视频加载失败`, 'error');
                    }
                } else {
                    addLog('❌ 智能视频加载器未找到', 'error');
                }
                
            } catch (error) {
                addLog(`❌ 测试异常: ${error.message}`, 'error');
            }
        }
        
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '<div class="log-entry log-info">📦 日志已清空</div>';
        }
        
        async function showConfig() {
            try {
                if (window.videoLoader) {
                    const config = window.videoLoader.getConfig();
                    addLog(`📋 当前配置: ${JSON.stringify(config, null, 2)}`, 'info');
                } else {
                    addLog('❌ 智能视频加载器未找到', 'error');
                }
            } catch (error) {
                addLog(`❌ 获取配置失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('📦 智能视频加载器测试页面已加载', 'success');
            
            if (window.videoLoader) {
                addLog('✅ 智能视频加载器已就绪', 'success');
            } else {
                addLog('❌ 智能视频加载器未加载', 'error');
            }
        });
    </script>
</body>
</html>
