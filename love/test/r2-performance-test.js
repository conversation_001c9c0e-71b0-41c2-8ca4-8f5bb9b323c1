#!/usr/bin/env node

/**
 * Cloudflare R2 性能测试脚本
 * 
 * 功能：
 * 1. 测试R2存储桶的视频加载性能
 * 2. 验证公共域名访问权限
 * 3. 测量加载时间和成功率
 * 4. 模拟不同网络环境
 * 5. 生成详细的性能报告
 * 
 * 使用方法：
 * node test/r2-performance-test.js
 * 
 * 环境要求：
 * - VIDEO_LOADING_STRATEGY=R2_ONLY
 * - CLOUDFLARE_R2_ENABLED=true
 * - 所有R2配置变量已设置
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');
require('dotenv').config({ path: path.join(__dirname, '../config/.env') });

class R2PerformanceTest {
    constructor() {
        this.testResults = {
            timestamp: new Date().toISOString(),
            strategy: 'R2_ONLY',
            videos: [],
            summary: {
                totalTests: 0,
                successCount: 0,
                failCount: 0,
                averageLoadTime: 0,
                minLoadTime: Infinity,
                maxLoadTime: 0,
                totalDataTransferred: 0
            },
            networkTests: [],
            errors: []
        };
        
        // 测试视频列表
        this.videos = ['home', 'anniversary', 'meetings', 'memorial', 'together-days'];
        
        // R2配置
        this.r2Domain = process.env.CLOUDFLARE_R2_DOMAIN;
        this.r2Timeout = parseInt(process.env.R2_TIMEOUT) || 6000;
        
        console.log('🧪 R2性能测试初始化');
        console.log(`   R2域名: ${this.r2Domain}`);
        console.log(`   超时设置: ${this.r2Timeout}ms`);
        console.log(`   测试视频: ${this.videos.length}个`);
    }

    /**
     * 验证配置
     */
    validateConfig() {
        console.log('\n📋 验证测试配置...');
        
        // 检查必需的环境变量
        const requiredVars = [
            'CLOUDFLARE_R2_DOMAIN',
            'CLOUDFLARE_R2_BUCKET',
            'VIDEO_DELIVERY_ENABLED'
        ];
        
        const missing = requiredVars.filter(varName => !process.env[varName]);
        
        if (missing.length > 0) {
            console.error('❌ 缺少必需的环境变量:');
            missing.forEach(varName => console.error(`   - ${varName}`));
            throw new Error('配置验证失败');
        }
        
        // 检查策略设置
        const strategy = process.env.VIDEO_LOADING_STRATEGY;
        if (strategy !== 'R2_ONLY') {
            console.warn(`⚠️ 当前策略: ${strategy}, 建议设置为 R2_ONLY`);
        }
        
        // 检查R2是否启用
        if (process.env.CLOUDFLARE_R2_ENABLED !== 'true') {
            throw new Error('R2未启用，请设置 CLOUDFLARE_R2_ENABLED=true');
        }
        
        console.log('✅ 配置验证通过');
    }

    /**
     * 测试单个视频的加载性能
     */
    async testVideoLoad(videoName) {
        const url = `https://${this.r2Domain}/love-website/videos/${videoName}.mp4`;
        
        console.log(`\n🎬 测试视频: ${videoName}`);
        console.log(`   URL: ${url}`);
        
        const testResult = {
            videoName,
            url,
            startTime: Date.now(),
            endTime: null,
            loadTime: null,
            success: false,
            statusCode: null,
            contentLength: null,
            error: null,
            headers: {}
        };
        
        return new Promise((resolve) => {
            const startTime = Date.now();
            
            // 创建HTTP请求
            const request = https.get(url, {
                timeout: this.r2Timeout,
                headers: {
                    'User-Agent': 'Love-Website-R2-Performance-Test/1.0',
                    'Accept': 'video/mp4,video/*,*/*'
                }
            }, (response) => {
                testResult.statusCode = response.statusCode;
                testResult.headers = response.headers;
                testResult.contentLength = parseInt(response.headers['content-length']) || 0;
                
                let dataReceived = 0;
                
                // 监听数据接收
                response.on('data', (chunk) => {
                    dataReceived += chunk.length;
                    
                    // 如果接收到足够的数据（前1MB），认为加载成功
                    if (dataReceived >= 1024 * 1024) {
                        testResult.endTime = Date.now();
                        testResult.loadTime = testResult.endTime - startTime;
                        testResult.success = true;
                        
                        console.log(`   ✅ 加载成功: ${testResult.loadTime}ms`);
                        console.log(`   状态码: ${testResult.statusCode}`);
                        console.log(`   内容长度: ${this.formatFileSize(testResult.contentLength)}`);
                        console.log(`   已接收: ${this.formatFileSize(dataReceived)}`);
                        
                        // 中断请求，我们只需要测试连接性能
                        request.destroy();
                        resolve(testResult);
                    }
                });
                
                // 响应结束
                response.on('end', () => {
                    if (!testResult.success) {
                        testResult.endTime = Date.now();
                        testResult.loadTime = testResult.endTime - startTime;
                        testResult.success = response.statusCode === 200;
                        
                        if (testResult.success) {
                            console.log(`   ✅ 完整加载: ${testResult.loadTime}ms`);
                        } else {
                            console.log(`   ❌ 加载失败: HTTP ${testResult.statusCode}`);
                        }
                    }
                    resolve(testResult);
                });
                
                // 响应错误
                response.on('error', (error) => {
                    testResult.endTime = Date.now();
                    testResult.loadTime = testResult.endTime - startTime;
                    testResult.error = error.message;
                    
                    console.log(`   ❌ 响应错误: ${error.message}`);
                    resolve(testResult);
                });
            });
            
            // 请求超时
            request.on('timeout', () => {
                testResult.endTime = Date.now();
                testResult.loadTime = testResult.endTime - startTime;
                testResult.error = 'Request timeout';
                
                console.log(`   ⏰ 请求超时: ${testResult.loadTime}ms`);
                request.destroy();
                resolve(testResult);
            });
            
            // 请求错误
            request.on('error', (error) => {
                testResult.endTime = Date.now();
                testResult.loadTime = testResult.endTime - startTime;
                testResult.error = error.message;
                
                console.log(`   ❌ 请求错误: ${error.message}`);
                resolve(testResult);
            });
        });
    }

    /**
     * 测试网络连接性
     */
    async testNetworkConnectivity() {
        console.log('\n🌐 测试网络连接性...');
        
        const testUrls = [
            { name: 'Cloudflare CDN', url: 'https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js' },
            { name: 'R2 Domain', url: `https://${this.r2Domain}` },
            { name: 'Google DNS', url: 'https://dns.google/resolve?name=cloudflare.com&type=A' }
        ];
        
        for (const test of testUrls) {
            try {
                const startTime = Date.now();
                
                await new Promise((resolve, reject) => {
                    const request = https.get(test.url, { timeout: 5000 }, (response) => {
                        const loadTime = Date.now() - startTime;
                        
                        console.log(`   ✅ ${test.name}: ${loadTime}ms (HTTP ${response.statusCode})`);
                        
                        this.testResults.networkTests.push({
                            name: test.name,
                            url: test.url,
                            loadTime,
                            statusCode: response.statusCode,
                            success: true
                        });
                        
                        resolve();
                    });
                    
                    request.on('timeout', () => {
                        console.log(`   ⏰ ${test.name}: 超时`);
                        request.destroy();
                        reject(new Error('Timeout'));
                    });
                    
                    request.on('error', reject);
                });
                
            } catch (error) {
                console.log(`   ❌ ${test.name}: ${error.message}`);
                
                this.testResults.networkTests.push({
                    name: test.name,
                    url: test.url,
                    loadTime: null,
                    statusCode: null,
                    success: false,
                    error: error.message
                });
            }
        }
    }

    /**
     * 执行完整的R2性能测试
     */
    async runFullTest() {
        console.log('🚀 开始R2存储桶分层测试验证\n');
        
        try {
            // 1. 验证配置
            this.validateConfig();
            
            // 2. 测试网络连接性
            await this.testNetworkConnectivity();
            
            // 3. 测试所有视频
            console.log('\n🎬 开始视频加载测试...');
            
            for (const videoName of this.videos) {
                const result = await this.testVideoLoad(videoName);
                this.testResults.videos.push(result);
                
                // 更新统计信息
                this.testResults.summary.totalTests++;
                
                if (result.success) {
                    this.testResults.summary.successCount++;
                    this.testResults.summary.totalDataTransferred += (result.contentLength || 0);
                    
                    if (result.loadTime < this.testResults.summary.minLoadTime) {
                        this.testResults.summary.minLoadTime = result.loadTime;
                    }
                    if (result.loadTime > this.testResults.summary.maxLoadTime) {
                        this.testResults.summary.maxLoadTime = result.loadTime;
                    }
                } else {
                    this.testResults.summary.failCount++;
                    this.testResults.errors.push({
                        video: videoName,
                        error: result.error || `HTTP ${result.statusCode}`
                    });
                }
                
                // 测试间隔
                await this.sleep(1000);
            }
            
            // 4. 计算平均加载时间
            const successfulTests = this.testResults.videos.filter(v => v.success);
            if (successfulTests.length > 0) {
                this.testResults.summary.averageLoadTime = 
                    successfulTests.reduce((sum, v) => sum + v.loadTime, 0) / successfulTests.length;
            }
            
            // 5. 生成测试报告
            this.generateReport();
            
            // 6. 保存测试结果
            this.saveResults();
            
            return this.testResults.summary.failCount === 0;
            
        } catch (error) {
            console.error('\n❌ 测试过程发生错误:', error.message);
            this.testResults.errors.push({
                type: 'system',
                error: error.message
            });
            return false;
        }
    }

    /**
     * 生成测试报告
     */
    generateReport() {
        const summary = this.testResults.summary;
        
        console.log('\n📊 R2性能测试报告');
        console.log('=' .repeat(50));
        
        console.log(`测试时间: ${this.testResults.timestamp}`);
        console.log(`测试策略: ${this.testResults.strategy}`);
        console.log(`R2域名: ${this.r2Domain}`);
        console.log('');
        
        console.log('📈 测试结果统计:');
        console.log(`   总测试数: ${summary.totalTests}`);
        console.log(`   成功数: ${summary.successCount}`);
        console.log(`   失败数: ${summary.failCount}`);
        console.log(`   成功率: ${((summary.successCount / summary.totalTests) * 100).toFixed(1)}%`);
        console.log('');
        
        if (summary.successCount > 0) {
            console.log('⏱️ 性能指标:');
            console.log(`   平均加载时间: ${Math.round(summary.averageLoadTime)}ms`);
            console.log(`   最快加载时间: ${summary.minLoadTime}ms`);
            console.log(`   最慢加载时间: ${summary.maxLoadTime}ms`);
            console.log(`   总数据传输: ${this.formatFileSize(summary.totalDataTransferred)}`);
            console.log('');
        }
        
        // 详细结果
        console.log('📋 详细测试结果:');
        this.testResults.videos.forEach(video => {
            const status = video.success ? '✅' : '❌';
            const time = video.loadTime ? `${video.loadTime}ms` : 'N/A';
            const size = video.contentLength ? this.formatFileSize(video.contentLength) : 'N/A';
            
            console.log(`   ${status} ${video.videoName}: ${time} (${size})`);
            if (!video.success && video.error) {
                console.log(`      错误: ${video.error}`);
            }
        });
        
        // 错误汇总
        if (this.testResults.errors.length > 0) {
            console.log('\n❌ 错误汇总:');
            this.testResults.errors.forEach(error => {
                console.log(`   - ${error.video || error.type}: ${error.error}`);
            });
        }
        
        // 测试结论
        console.log('\n🎯 测试结论:');
        if (summary.failCount === 0) {
            console.log('   ✅ R2存储桶测试完全通过');
            console.log('   ✅ 所有视频加载正常');
            console.log('   ✅ 性能指标达标');
            console.log('   ✅ 可以进入下一阶段测试');
        } else {
            console.log('   ❌ R2存储桶测试存在问题');
            console.log('   ❌ 需要修复失败项目后重新测试');
            console.log('   ❌ 不能进入下一阶段');
        }
    }

    /**
     * 保存测试结果到日志文件
     */
    saveResults() {
        const logDir = path.join(__dirname, '../logs');
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }
        
        const logFile = path.join(logDir, 'r2-test-results.log');
        const jsonFile = path.join(logDir, 'r2-test-results.json');
        
        // 保存JSON格式的详细结果
        fs.writeFileSync(jsonFile, JSON.stringify(this.testResults, null, 2));
        
        // 保存简化的日志格式
        const logContent = [
            `R2性能测试结果 - ${this.testResults.timestamp}`,
            `策略: ${this.testResults.strategy}`,
            `成功率: ${((this.testResults.summary.successCount / this.testResults.summary.totalTests) * 100).toFixed(1)}%`,
            `平均加载时间: ${Math.round(this.testResults.summary.averageLoadTime)}ms`,
            `错误数: ${this.testResults.errors.length}`,
            ''
        ].join('\n');
        
        fs.appendFileSync(logFile, logContent);
        
        console.log(`\n💾 测试结果已保存:`);
        console.log(`   详细结果: ${jsonFile}`);
        console.log(`   日志文件: ${logFile}`);
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 延迟函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 主执行函数
async function main() {
    const tester = new R2PerformanceTest();
    
    try {
        const success = await tester.runFullTest();
        
        if (success) {
            console.log('\n🎉 R2存储桶分层测试验证完成！');
            process.exit(0);
        } else {
            console.log('\n💥 R2存储桶分层测试验证失败！');
            process.exit(1);
        }
        
    } catch (error) {
        console.error('\n❌ 测试执行失败:', error);
        process.exit(1);
    }
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    console.error('❌ 未捕获的异常:', error);
    process.exit(1);
});

// 执行测试
if (require.main === module) {
    main();
}

module.exports = R2PerformanceTest;
