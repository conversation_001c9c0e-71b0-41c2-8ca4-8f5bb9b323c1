<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三级折叠功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>三级折叠功能测试</h1>
        <div id="test-results"></div>
        <button onclick="runTests()">运行测试</button>
        <button onclick="testAPI()">测试API</button>
        <button onclick="testGrouping()">测试分组逻辑</button>
    </div>

    <script>
        const API_BASE_URL = '/api';
        
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }

        async function testAPI() {
            log('开始测试API...', 'info');
            try {
                const response = await fetch(`${API_BASE_URL}/messages`);
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ API测试成功，获取到 ${data.data.length} 条留言`, 'success');
                    
                    // 检查是否有2025年的多月份数据
                    const messages2025 = data.data.filter(msg => msg.beijing_date.startsWith('2025'));
                    const months2025 = [...new Set(messages2025.map(msg => msg.beijing_date.substring(0, 7)))];
                    
                    log(`2025年留言数量: ${messages2025.length}`, 'info');
                    log(`2025年月份: ${months2025.join(', ')}`, 'info');
                    
                    if (months2025.length > 1) {
                        log('✅ 2025年有多个月份的数据，应该触发三级折叠', 'success');
                    } else {
                        log('⚠️ 2025年只有一个月份的数据，不会触发三级折叠', 'error');
                    }
                } else {
                    log(`❌ API测试失败: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`❌ API请求失败: ${error.message}`, 'error');
            }
        }

        async function testGrouping() {
            log('开始测试分组逻辑...', 'info');
            try {
                const response = await fetch(`${API_BASE_URL}/messages`);
                const data = await response.json();
                
                if (!data.success) {
                    log(`❌ 获取数据失败: ${data.message}`, 'error');
                    return;
                }

                // 模拟分组逻辑
                const messages = data.data;
                const groups = {};
                const now = new Date();

                messages.forEach(message => {
                    const messageDate = new Date(message.timestamp);
                    const messageYear = messageDate.getFullYear();
                    
                    // 只处理年份分组
                    if (messageYear < now.getFullYear() || 
                        (messageYear === now.getFullYear() && 
                         (now.getTime() - messageDate.getTime()) > 30 * 24 * 60 * 60 * 1000)) {
                        
                        const groupKey = `year-${messageYear}`;
                        if (!groups[groupKey]) {
                            groups[groupKey] = {
                                title: `${messageYear}年`,
                                messages: [],
                                months: new Set()
                            };
                        }
                        groups[groupKey].messages.push(message);
                        groups[groupKey].months.add(message.beijing_date.substring(0, 7));
                    }
                });

                log(`检测到年份分组: ${Object.keys(groups).length}个`, 'info');
                
                Object.entries(groups).forEach(([groupKey, group]) => {
                    const monthCount = group.months.size;
                    log(`${group.title}: ${group.messages.length}条留言, ${monthCount}个月份`, 'info');
                    
                    if (monthCount > 1) {
                        log(`✅ ${group.title} 应该显示月份子分组`, 'success');
                        log(`月份列表: ${Array.from(group.months).join(', ')}`, 'info');
                    } else {
                        log(`ℹ️ ${group.title} 不需要月份子分组`, 'info');
                    }
                });

            } catch (error) {
                log(`❌ 分组测试失败: ${error.message}`, 'error');
            }
        }

        async function runTests() {
            document.getElementById('test-results').innerHTML = '';
            log('开始运行所有测试...', 'info');
            
            await testAPI();
            await testGrouping();
            
            log('所有测试完成！', 'success');
        }

        // 页面加载时自动运行测试
        window.addEventListener('load', runTests);
    </script>
</body>
</html>
