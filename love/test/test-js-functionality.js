/**
 * JavaScript功能测试脚本
 * 测试星空背景保障机制的JavaScript功能
 */

// const puppeteer = require('puppeteer'); // 延迟加载

async function testStarryBackgroundFunctionality() {
    console.log('🧪 开始测试星空背景JavaScript功能...');
    
    let browser;
    try {
        // 动态加载puppeteer
        const puppeteer = require('puppeteer');

        // 启动浏览器
        browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const page = await browser.newPage();
        
        // 监听控制台输出
        page.on('console', msg => {
            const type = msg.type();
            const text = msg.text();
            if (type === 'error') {
                console.log(`❌ 浏览器错误: ${text}`);
            } else if (text.includes('StarryTest') || text.includes('星空')) {
                console.log(`📝 测试日志: ${text}`);
            }
        });
        
        // 监听页面错误
        page.on('pageerror', error => {
            console.log(`❌ 页面错误: ${error.message}`);
        });
        
        // 访问测试页面
        console.log('🌐 访问测试页面...');
        await page.goto('http://localhost:1314/test/test-starry-background.html', {
            waitUntil: 'networkidle0',
            timeout: 30000
        });
        
        // 等待页面加载完成
        await page.waitForTimeout(2000);
        
        // 检查关键元素是否存在
        console.log('🔍 检查页面元素...');
        const elements = await page.evaluate(() => {
            return {
                videoElement: !!document.getElementById('testVideo'),
                videoContainer: !!document.getElementById('videoBackground'),
                statusPanel: !!document.getElementById('statusPanel'),
                logPanel: !!document.getElementById('logPanel'),
                videoLoader: !!window.videoLoader,
                VideoLoader: !!window.VideoLoader
            };
        });
        
        console.log('📊 元素检查结果:');
        for (const [key, value] of Object.entries(elements)) {
            console.log(`  ${value ? '✅' : '❌'} ${key}: ${value}`);
        }
        
        // 等待初始化完成
        console.log('⏳ 等待初始化完成...');
        await page.waitForTimeout(3000);
        
        // 检查状态面板内容
        const statusContent = await page.evaluate(() => {
            const statusElement = document.getElementById('currentStatus');
            return statusElement ? statusElement.textContent : null;
        });
        
        console.log(`📋 当前状态: ${statusContent}`);
        
        // 测试主题切换功能
        console.log('🎨 测试主题切换功能...');
        
        const themes = ['home', 'anniversary', 'meetings', 'memorial', 'together-days'];
        
        for (const theme of themes) {
            console.log(`  测试${theme}主题...`);
            
            // 点击主题按钮
            await page.evaluate((themeName) => {
                window.handleTestPageTheme(themeName);
            }, theme);
            
            // 等待处理完成
            await page.waitForTimeout(1000);
            
            // 检查是否应用了星空背景
            const hasStarryBackground = await page.evaluate(() => {
                const container = document.getElementById('videoBackground');
                return container && container.classList.contains('starry-fallback');
            });
            
            console.log(`    ${hasStarryBackground ? '✅' : '❌'} ${theme}主题星空背景: ${hasStarryBackground}`);
        }
        
        // 测试故障模拟功能
        console.log('🚨 测试故障模拟功能...');
        await page.evaluate(() => {
            window.handleSimulateFailure('all');
        });
        
        await page.waitForTimeout(1000);
        
        // 检查日志内容
        const logEntries = await page.evaluate(() => {
            const logContent = document.getElementById('logContent');
            return logContent ? logContent.children.length : 0;
        });
        
        console.log(`📝 日志条目数: ${logEntries}`);
        
        // 获取最终状态
        const finalStatus = await page.evaluate(() => {
            return {
                currentStatus: document.getElementById('currentStatus')?.textContent,
                currentLayer: document.getElementById('currentLayer')?.textContent,
                loadTime: document.getElementById('loadTime')?.textContent,
                attemptCount: document.getElementById('attemptCount')?.textContent
            };
        });
        
        console.log('📊 最终状态:');
        for (const [key, value] of Object.entries(finalStatus)) {
            console.log(`  ${key}: ${value}`);
        }
        
        console.log('✅ JavaScript功能测试完成');
        return true;
        
    } catch (error) {
        console.error(`❌ 测试失败: ${error.message}`);
        return false;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// 检查是否安装了puppeteer
async function checkPuppeteer() {
    try {
        require('puppeteer');
        return true;
    } catch (error) {
        console.log('⚠️ Puppeteer未安装，跳过浏览器测试');
        console.log('💡 可以手动访问 http://localhost:1314/test/test-starry-background.html 进行测试');
        return false;
    }
}

// 简单的功能检查（不需要puppeteer）
async function simpleCheck() {
    console.log('🔍 执行简单功能检查...');
    
    const http = require('http');
    
    // 检查测试页面是否可访问
    const testPageCheck = await new Promise((resolve) => {
        const req = http.get('http://localhost:1314/test/test-starry-background.html', (res) => {
            resolve(res.statusCode === 200);
        });
        req.on('error', () => resolve(false));
        req.setTimeout(5000, () => {
            req.destroy();
            resolve(false);
        });
    });
    
    console.log(`${testPageCheck ? '✅' : '❌'} 测试页面可访问: ${testPageCheck}`);
    
    // 检查video-loader.js是否可访问
    const jsFileCheck = await new Promise((resolve) => {
        const req = http.get('http://localhost:1314/src/client/scripts/video-loader.js', (res) => {
            resolve(res.statusCode === 200);
        });
        req.on('error', () => resolve(false));
        req.setTimeout(5000, () => {
            req.destroy();
            resolve(false);
        });
    });
    
    console.log(`${jsFileCheck ? '✅' : '❌'} video-loader.js可访问: ${jsFileCheck}`);
    
    // 检查CSS文件是否可访问
    const cssFileCheck = await new Promise((resolve) => {
        const req = http.get('http://localhost:1314/src/client/styles/starry-background.css', (res) => {
            resolve(res.statusCode === 200);
        });
        req.on('error', () => resolve(false));
        req.setTimeout(5000, () => {
            req.destroy();
            resolve(false);
        });
    });
    
    console.log(`${cssFileCheck ? '✅' : '❌'} starry-background.css可访问: ${cssFileCheck}`);
    
    return testPageCheck && jsFileCheck && cssFileCheck;
}

// 主函数
async function main() {
    console.log('🌟 星空背景JavaScript功能测试\n');
    
    // 先执行简单检查
    const simpleResult = await simpleCheck();
    
    if (!simpleResult) {
        console.log('❌ 基础检查失败，请检查服务器状态');
        process.exit(1);
    }
    
    // 检查是否可以执行浏览器测试
    const hasPuppeteer = await checkPuppeteer();
    
    if (hasPuppeteer) {
        const browserResult = await testStarryBackgroundFunctionality();
        process.exit(browserResult ? 0 : 1);
    } else {
        console.log('\n✅ 基础检查通过');
        console.log('🌐 请手动访问以下URL进行完整测试:');
        console.log('   https://love.yuh.cool/test/test-starry-background.html');
        process.exit(0);
    }
}

if (require.main === module) {
    main();
}
