/**
 * 前端页面四层架构迁移策略脚本
 * 
 * 功能：
 * 1. 分析现有页面视频加载代码
 * 2. 生成迁移计划
 * 3. 提供新旧系统切换开关
 * 4. 验证迁移完整性
 * 
 * @version 1.0.0
 * <AUTHOR> Project Team
 */

const fs = require('fs');
const path = require('path');

class MigrationStrategy {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.pagesDir = path.join(this.projectRoot, 'src/client/pages');
        this.scriptsDir = path.join(this.projectRoot, 'src/client/scripts');
        
        // 页面配置
        this.pages = [
            { name: 'index', file: 'index.html', theme: 'home' },
            { name: 'together-days', file: 'together-days.html', theme: 'together-days' },
            { name: 'anniversary', file: 'anniversary.html', theme: 'anniversary' },
            { name: 'meetings', file: 'meetings.html', theme: 'meetings' },
            { name: 'memorial', file: 'memorial.html', theme: 'memorial' }
        ];
        
        // 迁移状态
        this.migrationStatus = {
            analyzed: false,
            planned: false,
            executed: false,
            verified: false
        };
    }

    /**
     * 执行完整迁移流程
     */
    async executeMigration() {
        console.log('🚀 开始前端页面四层架构迁移...\n');
        
        try {
            // 1. 分析现有代码
            await this.analyzeExistingCode();
            
            // 2. 生成迁移计划
            await this.generateMigrationPlan();
            
            // 3. 执行迁移
            await this.executeMigrationPlan();
            
            // 4. 验证迁移结果
            await this.verifyMigration();
            
            console.log('\n✅ 迁移完成！所有页面已成功集成四层架构');
            
        } catch (error) {
            console.error('\n❌ 迁移失败:', error.message);
            await this.rollbackMigration();
        }
    }

    /**
     * 分析现有代码
     */
    async analyzeExistingCode() {
        console.log('📊 分析现有页面代码...');
        
        const analysis = {
            pages: [],
            videoSources: [],
            loadingLogic: [],
            themeBackgrounds: []
        };
        
        for (const page of this.pages) {
            const filePath = path.join(this.pagesDir, page.file);
            
            if (!fs.existsSync(filePath)) {
                console.warn(`⚠️ 页面文件不存在: ${page.file}`);
                continue;
            }
            
            const content = fs.readFileSync(filePath, 'utf8');
            
            // 分析视频源
            const videoSourceMatch = content.match(/src="([^"]*\.mp4)"/);
            const videoSource = videoSourceMatch ? videoSourceMatch[1] : null;
            
            // 分析主题背景
            const themeMatch = content.match(/linear-gradient\([^)]+\)/);
            const themeBackground = themeMatch ? themeMatch[0] : null;
            
            // 分析加载逻辑
            const hasLoadingLogic = content.includes('video.addEventListener');
            const hasTimeout = content.includes('setTimeout');
            
            const pageAnalysis = {
                name: page.name,
                file: page.file,
                videoSource,
                themeBackground,
                hasLoadingLogic,
                hasTimeout,
                compatible: true
            };
            
            analysis.pages.push(pageAnalysis);
            
            console.log(`  ✅ ${page.name}: 视频源=${videoSource ? '✓' : '✗'}, 加载逻辑=${hasLoadingLogic ? '✓' : '✗'}`);
        }
        
        this.analysis = analysis;
        this.migrationStatus.analyzed = true;
        
        console.log(`📋 分析完成: ${analysis.pages.length}个页面\n`);
    }

    /**
     * 生成迁移计划
     */
    async generateMigrationPlan() {
        console.log('📝 生成迁移计划...');
        
        const plan = {
            steps: [],
            backupFiles: [],
            newFiles: [],
            modifications: []
        };
        
        for (const pageAnalysis of this.analysis.pages) {
            // 步骤1: 备份原文件
            plan.steps.push({
                type: 'backup',
                description: `备份 ${pageAnalysis.file}`,
                source: path.join(this.pagesDir, pageAnalysis.file),
                backup: path.join(this.pagesDir, `${pageAnalysis.file}.backup`)
            });
            
            // 步骤2: 更新视频源路径
            plan.steps.push({
                type: 'update_video_source',
                description: `更新 ${pageAnalysis.name} 视频源路径`,
                file: pageAnalysis.file,
                oldPath: pageAnalysis.videoSource,
                newPath: `/src/client/assets/video-compressed/${pageAnalysis.name === 'index' ? 'home' : pageAnalysis.name}.mp4`
            });
            
            // 步骤3: 集成智能加载器
            plan.steps.push({
                type: 'integrate_smart_loader',
                description: `集成智能加载器到 ${pageAnalysis.name}`,
                file: pageAnalysis.file,
                pageName: pageAnalysis.name === 'index' ? 'home' : pageAnalysis.name
            });
            
            // 步骤4: 添加切换开关
            plan.steps.push({
                type: 'add_switch',
                description: `添加新旧系统切换开关到 ${pageAnalysis.name}`,
                file: pageAnalysis.file
            });
        }
        
        this.migrationPlan = plan;
        this.migrationStatus.planned = true;
        
        console.log(`📋 迁移计划生成完成: ${plan.steps.length}个步骤\n`);
    }

    /**
     * 执行迁移计划
     */
    async executeMigrationPlan() {
        console.log('🔧 执行迁移计划...');
        
        for (const step of this.migrationPlan.steps) {
            try {
                await this.executeStep(step);
                console.log(`  ✅ ${step.description}`);
            } catch (error) {
                console.error(`  ❌ ${step.description}: ${error.message}`);
                throw error;
            }
        }
        
        this.migrationStatus.executed = true;
        console.log('🔧 迁移计划执行完成\n');
    }

    /**
     * 执行单个迁移步骤
     */
    async executeStep(step) {
        switch (step.type) {
            case 'backup':
                if (fs.existsSync(step.source)) {
                    fs.copyFileSync(step.source, step.backup);
                }
                break;
                
            case 'update_video_source':
                await this.updateVideoSource(step);
                break;
                
            case 'integrate_smart_loader':
                await this.integrateSmartLoader(step);
                break;
                
            case 'add_switch':
                await this.addMigrationSwitch(step);
                break;
                
            default:
                throw new Error(`未知的迁移步骤类型: ${step.type}`);
        }
    }

    /**
     * 更新视频源路径
     */
    async updateVideoSource(step) {
        const filePath = path.join(this.pagesDir, step.file);
        let content = fs.readFileSync(filePath, 'utf8');
        
        if (step.oldPath && step.newPath) {
            content = content.replace(step.oldPath, step.newPath);
            fs.writeFileSync(filePath, content);
        }
    }

    /**
     * 集成智能加载器
     */
    async integrateSmartLoader(step) {
        const filePath = path.join(this.pagesDir, step.file);
        let content = fs.readFileSync(filePath, 'utf8');
        
        // 查找现有的视频加载逻辑
        const videoLoadingStart = content.indexOf('// 视频加载状态标记') || content.indexOf('const video = document.querySelector');
        const videoLoadingEnd = content.lastIndexOf('});') + 3;
        
        if (videoLoadingStart > -1 && videoLoadingEnd > videoLoadingStart) {
            // 生成新的集成代码
            const integrationCode = this.generateIntegrationCode(step.pageName);
            
            // 替换现有逻辑
            const beforeLoading = content.substring(0, videoLoadingStart);
            const afterLoading = content.substring(videoLoadingEnd);
            
            content = beforeLoading + integrationCode + afterLoading;
            fs.writeFileSync(filePath, content);
        }
    }

    /**
     * 生成智能加载器集成代码
     */
    generateIntegrationCode(pageName) {
        return `
        // 智能视频加载器集成 - 四层CDN架构
        const USE_SMART_LOADER = true; // 新旧系统切换开关
        
        if (USE_SMART_LOADER) {
            // 使用四层智能加载器
            console.log('🎬 启用四层智能视频加载器');
            
            // 确保智能加载器已加载
            if (typeof VideoLoader !== 'undefined') {
                VideoLoader.integrateWithPage({
                    pageName: '${pageName}',
                    videoSelector: '.video-background video',
                    loadingOverlaySelector: '#loadingOverlay',
                    loadingProgressSelector: '#loadingProgress'
                });
            } else {
                console.error('❌ 智能加载器未加载，降级到原有逻辑');
                loadVideoWithOriginalMethod();
            }
        } else {
            // 使用原有加载逻辑
            console.log('📼 使用原有视频加载逻辑');
            loadVideoWithOriginalMethod();
        }
        
        // 原有加载逻辑封装
        function loadVideoWithOriginalMethod() {
            // 这里保留原有的视频加载代码
            // [原有代码会在实际迁移时保留]
        }`;
    }

    /**
     * 添加迁移切换开关
     */
    async addMigrationSwitch(step) {
        const filePath = path.join(this.pagesDir, step.file);
        let content = fs.readFileSync(filePath, 'utf8');
        
        // 在head中添加智能加载器引用
        const headEndIndex = content.indexOf('</head>');
        if (headEndIndex > -1) {
            const loaderScript = `
    <!-- 智能视频加载器 - 四层CDN架构 -->
    <script src="/src/client/scripts/video-loader.js"></script>`;
            
            content = content.substring(0, headEndIndex) + loaderScript + '\n' + content.substring(headEndIndex);
            fs.writeFileSync(filePath, content);
        }
    }

    /**
     * 验证迁移结果
     */
    async verifyMigration() {
        console.log('🔍 验证迁移结果...');
        
        const verification = {
            success: true,
            issues: [],
            pages: []
        };
        
        for (const page of this.pages) {
            const filePath = path.join(this.pagesDir, page.file);
            const content = fs.readFileSync(filePath, 'utf8');
            
            const pageVerification = {
                name: page.name,
                hasSmartLoader: content.includes('video-loader.js'),
                hasSwitch: content.includes('USE_SMART_LOADER'),
                hasIntegration: content.includes('VideoLoader.integrateWithPage'),
                updatedVideoPath: content.includes('/src/client/assets/video-compressed/')
            };
            
            verification.pages.push(pageVerification);
            
            // 检查问题
            if (!pageVerification.hasSmartLoader) {
                verification.issues.push(`${page.name}: 缺少智能加载器引用`);
            }
            if (!pageVerification.hasSwitch) {
                verification.issues.push(`${page.name}: 缺少切换开关`);
            }
            if (!pageVerification.updatedVideoPath) {
                verification.issues.push(`${page.name}: 视频路径未更新`);
            }
            
            console.log(`  ${pageVerification.hasSmartLoader && pageVerification.hasSwitch ? '✅' : '❌'} ${page.name}`);
        }
        
        verification.success = verification.issues.length === 0;
        this.verification = verification;
        this.migrationStatus.verified = true;
        
        if (!verification.success) {
            console.log('\n⚠️ 发现问题:');
            verification.issues.forEach(issue => console.log(`  - ${issue}`));
            throw new Error('迁移验证失败');
        }
        
        console.log('🔍 验证完成\n');
    }

    /**
     * 回滚迁移
     */
    async rollbackMigration() {
        console.log('🔄 回滚迁移...');
        
        for (const page of this.pages) {
            const originalFile = path.join(this.pagesDir, page.file);
            const backupFile = path.join(this.pagesDir, `${page.file}.backup`);
            
            if (fs.existsSync(backupFile)) {
                fs.copyFileSync(backupFile, originalFile);
                fs.unlinkSync(backupFile);
                console.log(`  ✅ 恢复 ${page.file}`);
            }
        }
        
        console.log('🔄 回滚完成');
    }

    /**
     * 生成迁移报告
     */
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            status: this.migrationStatus,
            analysis: this.analysis,
            plan: this.migrationPlan,
            verification: this.verification
        };
        
        const reportPath = path.join(this.projectRoot, 'docs/migration-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log(`📊 迁移报告已生成: ${reportPath}`);
        return report;
    }
}

// 导出类和便捷函数
module.exports = MigrationStrategy;

// 如果直接运行此脚本
if (require.main === module) {
    const migration = new MigrationStrategy();
    
    // 解析命令行参数
    const args = process.argv.slice(2);
    const command = args[0] || 'migrate';
    
    switch (command) {
        case 'analyze':
            migration.analyzeExistingCode().then(() => {
                console.log('分析完成');
            });
            break;
            
        case 'plan':
            migration.analyzeExistingCode()
                .then(() => migration.generateMigrationPlan())
                .then(() => console.log('计划生成完成'));
            break;
            
        case 'migrate':
            migration.executeMigration()
                .then(() => migration.generateReport())
                .then(() => console.log('迁移完成'));
            break;
            
        case 'rollback':
            migration.rollbackMigration();
            break;
            
        default:
            console.log('用法: node migration-strategy.js [analyze|plan|migrate|rollback]');
    }
}
