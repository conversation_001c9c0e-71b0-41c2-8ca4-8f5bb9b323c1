#!/usr/bin/env node

/**
 * Cloudinary 多账户视频上传工具
 * 
 * 功能：基于config.js中的多账户配置实现智能页面映射上传
 * 路径：从 src/client/assets/video-compressed/ 上传到各个Cloudinary账户
 * 配置：基于config/config.js中的cloudinary多账户配置
 * 
 * 使用方法：
 * node scripts/upload-cloudinary.js
 * 
 * 环境要求：
 * - cloudinary 已安装
 * - config/.env 包含完整的Cloudinary API密钥
 * - src/client/assets/video-compressed/ 目录存在压缩视频
 */

const cloudinary = require('cloudinary').v2;
const fs = require('fs');
const path = require('path');
const config = require('../config/config.js');

// 配置验证
function validateConfig() {
    if (!config.cloudinary || !config.cloudinary.enabled) {
        console.error('❌ Cloudinary配置未启用');
        process.exit(1);
    }

    if (!config.cloudinary.accounts) {
        console.error('❌ 缺少Cloudinary账户配置');
        process.exit(1);
    }

    if (!config.cloudinary.pageMapping) {
        console.error('❌ 缺少页面映射配置');
        process.exit(1);
    }

    console.log('✅ Cloudinary配置验证通过');
    console.log(`   账户数量: ${Object.keys(config.cloudinary.accounts).length}`);
    console.log(`   页面映射: ${Object.keys(config.cloudinary.pageMapping).length}`);
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 获取页面对应的账户配置
function getAccountForPage(pageName) {
    const accountKey = config.cloudinary.pageMapping[pageName];
    if (!accountKey) {
        throw new Error(`页面 ${pageName} 没有对应的账户映射`);
    }

    const account = config.cloudinary.accounts[accountKey];
    if (!account) {
        throw new Error(`账户 ${accountKey} 配置不存在`);
    }

    return { accountKey, account };
}

// 上传单个视频文件
async function uploadVideo(pageName) {
    const sourceDir = path.join(__dirname, '../src/client/assets/video-compressed');
    const filePath = path.join(sourceDir, `${pageName}.mp4`);
    
    // 检查源文件是否存在
    if (!fs.existsSync(filePath)) {
        console.error(`❌ 源文件不存在: ${filePath}`);
        return false;
    }

    try {
        // 获取文件信息
        const fileStats = fs.statSync(filePath);
        const fileSize = fileStats.size;
        
        // 获取对应的账户配置
        const { accountKey, account } = getAccountForPage(pageName);
        
        console.log(`📹 上传 ${pageName}.mp4 (${formatFileSize(fileSize)}) 到账户 ${accountKey}...`);
        console.log(`   云名称: ${account.cloudName}`);
        console.log(`   文件夹: ${account.folder}`);

        // 配置当前账户
        cloudinary.config({
            cloud_name: account.cloudName,
            api_key: account.apiKey,
            api_secret: account.apiSecret
        });

        // 上传参数 - 针对大文件优化
        const uploadOptions = {
            resource_type: 'video',
            public_id: `${account.folder}/${pageName}`,
            overwrite: true,
            // 大文件上传设置
            chunk_size: 6000000, // 6MB chunks
            timeout: 120000, // 2分钟超时
            // 大文件异步处理 - 关键设置
            eager: [
                {
                    format: 'mp4',
                    quality: 'auto'
                }
            ],
            eager_async: true, // 异步处理
            // 元数据
            context: {
                'page': pageName,
                'account': accountKey,
                'upload_time': new Date().toISOString(),
                'compression': 'h265-crf14-16',
                'source': 'love-website-upload-script'
            }
        };

        // 执行上传
        const startTime = Date.now();
        const result = await cloudinary.uploader.upload(filePath, uploadOptions);
        const uploadTime = Date.now() - startTime;

        console.log(`✅ ${pageName}.mp4 上传成功`);
        console.log(`   Public ID: ${result.public_id}`);
        console.log(`   大小: ${formatFileSize(result.bytes)}`);
        console.log(`   用时: ${uploadTime}ms`);
        console.log(`   URL: ${result.secure_url}`);
        
        return true;

    } catch (error) {
        console.error(`❌ ${pageName}.mp4 上传失败:`, error.message);
        
        // 详细错误信息
        if (error.error && error.error.message) {
            console.error(`   详细错误: ${error.error.message}`);
        }
        if (error.http_code) {
            console.error(`   HTTP状态: ${error.http_code}`);
        }
        
        return false;
    }
}

// 清理账户中的旧文件（可选）
async function cleanupAccount(accountKey, account) {
    console.log(`🧹 清理账户 ${accountKey} (${account.cloudName})...`);
    
    // 配置当前账户
    cloudinary.config({
        cloud_name: account.cloudName,
        api_key: account.apiKey,
        api_secret: account.apiSecret
    });

    try {
        // 删除love-website文件夹下的所有视频资源
        const result = await cloudinary.api.delete_resources_by_prefix(`${account.folder}/`, {
            resource_type: 'video'
        });

        console.log(`✅ ${accountKey}: 清理了 ${Object.keys(result.deleted).length} 个视频文件`);
        return true;

    } catch (error) {
        console.error(`❌ ${accountKey}: 清理失败:`, error.message);
        return false;
    }
}

// 主上传函数
async function uploadAllVideos(options = {}) {
    console.log('☁️ 开始批量上传H.265压缩视频到Cloudinary多账户...\n');
    
    // 验证配置
    validateConfig();
    console.log('');

    // 视频文件列表（基于四层架构的5个页面）
    const videos = ['home', 'anniversary', 'meetings', 'memorial', 'together-days'];

    // 如果传入了特定视频列表，则使用该列表
    const targetVideos = process.argv.includes('--retry-failed')
        ? ['home', 'anniversary', 'memorial', 'together-days'] // 重试失败的文件
        : videos;
    
    // 可选：清理旧文件
    if (options.cleanup) {
        console.log('🧹 开始清理所有账户的旧文件...\n');
        
        for (const [accountKey, account] of Object.entries(config.cloudinary.accounts)) {
            if (accountKey !== 'BACKUP') { // 跳过备用账户
                await cleanupAccount(accountKey, account);
            }
        }
        console.log('');
    }

    // 统计信息
    let successCount = 0;
    let failCount = 0;
    const startTime = Date.now();

    // 逐个上传视频
    for (const video of targetVideos) {
        const success = await uploadVideo(video);
        if (success) {
            successCount++;
        } else {
            failCount++;
        }
        console.log(''); // 空行分隔
    }

    // 上传总结
    const totalTime = Date.now() - startTime;
    console.log('📊 上传完成统计:');
    console.log(`   成功: ${successCount}/${targetVideos.length}`);
    console.log(`   失败: ${failCount}/${targetVideos.length}`);
    console.log(`   总用时: ${totalTime}ms`);
    
    if (successCount === targetVideos.length) {
        console.log('\n🎉 所有视频上传成功！');
        console.log('\n📋 后续步骤:');
        console.log('   1. 验证Cloudinary URL访问');
        console.log('   2. 测试前端视频加载器');
        console.log('   3. 检查四层架构降级逻辑');
        
        console.log('\n🔗 测试URL示例:');
        targetVideos.forEach(video => {
            try {
                const { account } = getAccountForPage(video);
                const testUrl = `https://res.cloudinary.com/${account.cloudName}/video/upload/${account.folder}/${video}.mp4`;
                console.log(`   ${video}: ${testUrl}`);
            } catch (error) {
                console.log(`   ${video}: 配置错误`);
            }
        });
    } else {
        console.log('\n⚠️ 部分视频上传失败，请检查错误信息并重试');
        process.exit(1);
    }
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    console.error('❌ 未捕获的异常:', error);
    process.exit(1);
});

// 命令行参数处理
const args = process.argv.slice(2);
const options = {
    cleanup: args.includes('--cleanup') || args.includes('-c')
};

// 执行上传
if (require.main === module) {
    if (options.cleanup) {
        console.log('⚠️ 注意：将清理所有账户的旧文件！');
    }
    
    uploadAllVideos(options).catch(error => {
        console.error('❌ 上传过程发生错误:', error);
        process.exit(1);
    });
}

module.exports = { uploadAllVideos, uploadVideo, cleanupAccount };
