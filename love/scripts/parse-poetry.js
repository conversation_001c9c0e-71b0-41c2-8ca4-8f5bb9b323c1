// 解析诗词文件并生成JavaScript格式的诗词库
const fs = require('fs');
const path = require('path');

// 读取诗词文件
const poetryFile = path.join(__dirname, '../data/诗词筛选：积极情感篇_.txt');
const content = fs.readFileSync(poetryFile, 'utf-8');

// 解析每一行
const lines = content.split('\n').filter(line => line.trim());
const poems = [];

lines.forEach((line, index) => {
    console.log(`处理第${index + 1}行: ${line.substring(0, 50)}...`);

    // 匹配格式：数字. 诗句 (作品) 或 数字. 诗句 (作者·作品)
    const match = line.match(/^(\d+)\.\s*(.+?)\s*\((.+)\)$/);
    if (match) {
        const number = match[1];
        const text = match[2].trim();
        const authorWork = match[3].trim();

        console.log(`  匹配成功: ${text} | ${authorWork}`);

        // 解析作者和作品
        let author = "佚名";
        let work = "古典诗词";

        if (authorWork.includes('·')) {
            // 格式：作者·作品
            const parts = authorWork.split('·');
            if (parts.length >= 2) {
                author = parts[0].trim();
                work = parts[1].trim();

                // 处理特殊格式
                if (author.includes('汉乐府')) {
                    author = "佚名";
                    work = authorWork;
                } else if (author.startsWith('《')) {
                    author = "佚名";
                    work = authorWork;
                }
            }
        } else {
            // 格式：《作品》 或 汉乐府·《作品》
            if (authorWork.includes('汉乐府')) {
                author = "佚名";
                work = authorWork;
            } else if (authorWork.includes('诗经')) {
                author = "佚名";
                work = authorWork;
            } else if (authorWork.startsWith('《')) {
                author = "佚名";
                work = authorWork;
            } else {
                // 可能是 作者·作品 格式但没有·
                work = authorWork;
            }
        }

        poems.push({
            text: text,
            author: author,
            work: work
        });
    } else {
        console.log(`  匹配失败: ${line}`);
    }
});

// 生成JavaScript代码
let jsCode = `// 古典诗词数据库 - 精选积极情感的古典诗词（带作者和作品信息）
// 从您提供的${poems.length}首诗词库中扩充
const romanticQuotes = [\n`;

poems.forEach((poem, index) => {
    jsCode += `    {\n`;
    jsCode += `        text: "${poem.text}",\n`;
    jsCode += `        author: "${poem.author}",\n`;
    jsCode += `        work: "${poem.work}"\n`;
    jsCode += `    }`;
    if (index < poems.length - 1) {
        jsCode += ',';
    }
    jsCode += '\n';
});

jsCode += '];\n\n';

// 添加其余的代码（分类和函数）
jsCode += `// 中国古往今来经典情诗句库 - 按类别分类
const categorizedQuotes = {
    confession: [
        // 从主库中随机选择表白类诗句
        ...romanticQuotes.filter(q => 
            q.text.includes('君子好逑') || 
            q.text.includes('执子之手') || 
            q.text.includes('相知') ||
            q.text.includes('相思') ||
            q.text.includes('思君') ||
            q.text.includes('爱而不见')
        ).slice(0, 50)
    ],
    
    missing: [
        // 思念类诗句
        ...romanticQuotes.filter(q => 
            q.text.includes('思') || 
            q.text.includes('念') || 
            q.text.includes('一日不见') ||
            q.text.includes('相思') ||
            q.text.includes('如三月') ||
            q.text.includes('如三秋')
        ).slice(0, 50)
    ],
    
    promise: [
        // 承诺类诗句
        ...romanticQuotes.filter(q => 
            q.text.includes('偕老') || 
            q.text.includes('白头') || 
            q.text.includes('永以为好') ||
            q.text.includes('长命无绝衰') ||
            q.text.includes('生死相许') ||
            q.text.includes('同穴')
        ).slice(0, 50)
    ],
    
    sweet: [
        // 甜蜜类诗句
        ...romanticQuotes.filter(q => 
            q.text.includes('美') || 
            q.text.includes('华') || 
            q.text.includes('笑') ||
            q.text.includes('乐') ||
            q.text.includes('喜') ||
            q.text.includes('佼人')
        ).slice(0, 50)
    ]
};

// 获取随机浪漫话语
function getRandomQuote() {
    const quote = romanticQuotes[Math.floor(Math.random() * romanticQuotes.length)];
    return quote;
}

// 获取指定类别的随机话语
function getRandomQuoteByCategory(category) {
    const quotes = categorizedQuotes[category];
    if (!quotes || quotes.length === 0) return getRandomQuote();

    const quote = quotes[Math.floor(Math.random() * quotes.length)];
    return quote;
}

// 导出函数供其他文件使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        romanticQuotes,
        categorizedQuotes,
        getRandomQuote,
        getRandomQuoteByCategory
    };
}`;

// 写入新的诗词库文件
const outputFile = path.join(__dirname, '../romantic-quotes-new.js');
fs.writeFileSync(outputFile, jsCode, 'utf-8');

console.log(`成功解析 ${poems.length} 首诗词`);
console.log(`已生成新的诗词库文件: ${outputFile}`);
console.log('前10首诗词预览:');
poems.slice(0, 10).forEach((poem, index) => {
    console.log(`${index + 1}. ${poem.text} - ${poem.author} 《${poem.work}》`);
});
