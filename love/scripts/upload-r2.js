#!/usr/bin/env node

/**
 * Cloudflare R2 视频上传工具
 * 
 * 功能：批量上传H.265压缩视频到Cloudflare R2存储桶
 * 路径：从 src/client/assets/video-compressed/ 上传到 love-website/videos/
 * 配置：基于config/.env中的R2配置
 * 
 * 使用方法：
 * node scripts/upload-r2.js
 * 
 * 环境要求：
 * - @aws-sdk/client-s3 已安装
 * - config/.env 包含完整的R2配置
 * - src/client/assets/video-compressed/ 目录存在压缩视频
 */

const { S3Client, PutObjectCommand, HeadObjectCommand } = require('@aws-sdk/client-s3');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../config/.env') });

// R2客户端配置
const r2Client = new S3Client({
    region: 'auto',
    endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
    credentials: {
        accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
        secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
    },
    // 强制路径样式，R2要求
    forcePathStyle: true,
});

// 配置验证
function validateConfig() {
    const requiredVars = [
        'CLOUDFLARE_R2_ACCESS_KEY_ID',
        'CLOUDFLARE_R2_SECRET_ACCESS_KEY', 
        'CLOUDFLARE_R2_ENDPOINT',
        'CLOUDFLARE_R2_BUCKET'
    ];

    const missing = requiredVars.filter(varName => !process.env[varName]);
    
    if (missing.length > 0) {
        console.error('❌ 缺少必需的环境变量:');
        missing.forEach(varName => console.error(`   - ${varName}`));
        process.exit(1);
    }

    console.log('✅ R2配置验证通过');
    console.log(`   端点: ${process.env.CLOUDFLARE_R2_ENDPOINT}`);
    console.log(`   存储桶: ${process.env.CLOUDFLARE_R2_BUCKET}`);
    console.log(`   公共域名: ${process.env.CLOUDFLARE_R2_DOMAIN || '未配置'}`);
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 上传单个视频文件
async function uploadVideo(videoName) {
    const sourceDir = path.join(__dirname, '../src/client/assets/video-compressed');
    const filePath = path.join(sourceDir, `${videoName}.mp4`);
    
    // 检查源文件是否存在
    if (!fs.existsSync(filePath)) {
        console.error(`❌ 源文件不存在: ${filePath}`);
        return false;
    }

    try {
        // 读取文件
        const fileContent = fs.readFileSync(filePath);
        const fileSize = fileContent.length;
        
        console.log(`📹 上传 ${videoName}.mp4 (${formatFileSize(fileSize)})...`);

        // 构建R2对象键
        const objectKey = `love-website/videos/${videoName}.mp4`;

        // 上传命令
        const uploadCommand = new PutObjectCommand({
            Bucket: process.env.CLOUDFLARE_R2_BUCKET,
            Key: objectKey,
            Body: fileContent,
            ContentType: 'video/mp4',
            // R2特定的元数据
            Metadata: {
                'original-name': `${videoName}.mp4`,
                'upload-time': new Date().toISOString(),
                'compression': 'h265-crf14-16',
                'source': 'love-website-upload-script'
            }
        });

        // 执行上传
        const startTime = Date.now();
        await r2Client.send(uploadCommand);
        const uploadTime = Date.now() - startTime;

        // 验证上传结果
        const headCommand = new HeadObjectCommand({
            Bucket: process.env.CLOUDFLARE_R2_BUCKET,
            Key: objectKey
        });

        const headResult = await r2Client.send(headCommand);
        
        console.log(`✅ ${videoName}.mp4 上传成功`);
        console.log(`   大小: ${formatFileSize(headResult.ContentLength)}`);
        console.log(`   用时: ${uploadTime}ms`);
        console.log(`   ETag: ${headResult.ETag}`);
        
        // 显示公共访问URL（如果配置了公共域名）
        if (process.env.CLOUDFLARE_R2_DOMAIN) {
            const publicUrl = `https://${process.env.CLOUDFLARE_R2_DOMAIN}/${objectKey}`;
            console.log(`   公共URL: ${publicUrl}`);
        }
        
        return true;

    } catch (error) {
        console.error(`❌ ${videoName}.mp4 上传失败:`, error.message);
        
        // 详细错误信息
        if (error.Code) {
            console.error(`   错误代码: ${error.Code}`);
        }
        if (error.$metadata?.httpStatusCode) {
            console.error(`   HTTP状态: ${error.$metadata.httpStatusCode}`);
        }
        
        return false;
    }
}

// 主上传函数
async function uploadAllVideos() {
    console.log('🚀 开始批量上传H.265压缩视频到Cloudflare R2...\n');
    
    // 验证配置
    validateConfig();
    console.log('');

    // 视频文件列表（基于四层架构的5个页面）
    const videos = ['home', 'anniversary', 'meetings', 'memorial', 'together-days'];
    
    // 统计信息
    let successCount = 0;
    let failCount = 0;
    const startTime = Date.now();

    // 逐个上传视频
    for (const video of videos) {
        const success = await uploadVideo(video);
        if (success) {
            successCount++;
        } else {
            failCount++;
        }
        console.log(''); // 空行分隔
    }

    // 上传总结
    const totalTime = Date.now() - startTime;
    console.log('📊 上传完成统计:');
    console.log(`   成功: ${successCount}/${videos.length}`);
    console.log(`   失败: ${failCount}/${videos.length}`);
    console.log(`   总用时: ${totalTime}ms`);
    
    if (successCount === videos.length) {
        console.log('\n🎉 所有视频上传成功！');
        console.log('\n📋 后续步骤:');
        console.log('   1. 验证R2公共域名访问');
        console.log('   2. 测试前端视频加载器');
        console.log('   3. 检查四层架构降级逻辑');
        
        if (process.env.CLOUDFLARE_R2_DOMAIN) {
            console.log('\n🔗 测试URL示例:');
            console.log(`   https://${process.env.CLOUDFLARE_R2_DOMAIN}/love-website/videos/home.mp4`);
        }
    } else {
        console.log('\n⚠️ 部分视频上传失败，请检查错误信息并重试');
        process.exit(1);
    }
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    console.error('❌ 未捕获的异常:', error);
    process.exit(1);
});

// 执行上传
if (require.main === module) {
    uploadAllVideos().catch(error => {
        console.error('❌ 上传过程发生错误:', error);
        process.exit(1);
    });
}

module.exports = { uploadAllVideos, uploadVideo };
