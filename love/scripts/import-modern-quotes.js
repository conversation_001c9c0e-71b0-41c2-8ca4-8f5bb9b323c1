#!/usr/bin/env node

// 现代情话数据导入脚本
// 用于批量导入现代情话到数据库

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 数据库路径
const dbPath = path.join(__dirname, '../data/love_messages.db');

// 时间戳和日期格式化函数
const getCurrentTimestamp = () => Math.floor(Date.now() / 1000);

const getBeijingDateString = (timestamp) => {
    const date = new Date(timestamp * 1000);
    const beijingTime = new Date(date.getTime() + (8 * 60 * 60 * 1000));
    return beijingTime.toISOString().split('T')[0];
};

const getBeijingDateTimeString = (timestamp) => {
    const date = new Date(timestamp * 1000);
    const beijingTime = new Date(date.getTime() + (8 * 60 * 60 * 1000));
    return beijingTime.toISOString().replace('T', ' ').split('.')[0];
};

// 扩展的现代情话数据 - 补充到500+条
const additionalModernQuotes = [
    // 更多浪漫类情话
    { content: "我想要和你一起看遍四季的变化，春天的花开，夏天的绿荫，秋天的落叶，冬天的雪花。", source: "网络流行", category: "romantic" },
    { content: "你是我心中的那首歌，每当想起你，我的心就会跟着旋律跳动。", source: "网络流行", category: "romantic" },
    { content: "我想要和你一起做很多很多的梦，然后一起去实现它们。", source: "网络流行", category: "romantic" },
    { content: "你是我生命中最美的风景，无论走到哪里，我都想和你一起欣赏。", source: "网络流行", category: "romantic" },
    { content: "我想要和你一起变成小朋友，在阳光下奔跑，在雨中嬉戏。", source: "网络流行", category: "romantic" },
    { content: "你是我心中的那颗星，指引着我前进的方向。", source: "网络流行", category: "romantic" },
    { content: "我想要和你一起写下我们的故事，每一页都充满爱意。", source: "网络流行", category: "romantic" },
    { content: "你是我生命中最温暖的阳光，照亮了我的每一天。", source: "网络流行", category: "romantic" },
    { content: "我想要和你一起看遍世界的美景，然后告诉你，你比它们都美。", source: "网络流行", category: "romantic" },
    { content: "你是我心中永远的诗，我愿意为你吟诵一生。", source: "网络流行", category: "romantic" },

    // 更多甜蜜类情话
    { content: "你是我的小确幸，每天醒来第一个想到的就是你。", source: "网络流行", category: "sweet" },
    { content: "我想要和你一起吃遍世界的美食，然后说你做的最好吃。", source: "网络流行", category: "sweet" },
    { content: "你是我的小太阳，温暖着我的心房。", source: "网络流行", category: "sweet" },
    { content: "我想要和你一起看电影，然后在你害怕的时候抱紧你。", source: "网络流行", category: "sweet" },
    { content: "你是我的小棉袄，让我在寒冷的冬天也感到温暖。", source: "网络流行", category: "sweet" },
    { content: "我想要和你一起散步，牵着你的手走过每一条街道。", source: "网络流行", category: "sweet" },
    { content: "你是我的小幸运，让我的生活充满惊喜。", source: "网络流行", category: "sweet" },
    { content: "我想要和你一起听音乐，然后为你唱我们的歌。", source: "网络流行", category: "sweet" },
    { content: "你是我的小宝贝，我想要保护你一辈子。", source: "网络流行", category: "sweet" },
    { content: "我想要和你一起做饭，然后一起品尝我们的爱情。", source: "网络流行", category: "sweet" },

    // 更多深情类情话
    { content: "我愿意用我的一生去爱你，直到时间的尽头。", source: "网络流行", category: "deep" },
    { content: "你是我心中最深的爱，也是我最珍贵的宝藏。", source: "网络流行", category: "deep" },
    { content: "我想要和你一起面对人生的所有挑战，无论风雨。", source: "网络流行", category: "deep" },
    { content: "你是我生命中最重要的人，没有你我无法想象我的未来。", source: "网络流行", category: "deep" },
    { content: "我愿意为你放弃整个世界，因为你就是我的世界。", source: "网络流行", category: "deep" },
    { content: "你是我心中永远的春天，让我的世界充满生机和希望。", source: "网络流行", category: "deep" },
    { content: "我想要和你一起创造属于我们的回忆，每一个都珍贵无比。", source: "网络流行", category: "deep" },
    { content: "你是我生命中最美的奇迹，让我相信爱情的力量。", source: "网络流行", category: "deep" },
    { content: "我愿意陪你走过人生的每一个阶段，从青春到白头。", source: "网络流行", category: "deep" },
    { content: "你是我心中最真挚的情感，也是我最纯真的快乐。", source: "网络流行", category: "deep" },

    // 更多幽默类情话
    { content: "你知道我最喜欢什么吗？左眼是你，右眼也是你。", source: "网络流行", category: "funny" },
    { content: "你知道我为什么要学游泳吗？因为我想游到你心里。", source: "网络流行", category: "funny" },
    { content: "你知道我最喜欢什么运动吗？和你一起心跳。", source: "网络流行", category: "funny" },
    { content: "你知道我最喜欢什么电影吗？你的每一个表情。", source: "网络流行", category: "funny" },
    { content: "你知道我最喜欢什么音乐吗？你说话的声音。", source: "网络流行", category: "funny" },
    { content: "你知道我最喜欢什么风景吗？你在我眼中的样子。", source: "网络流行", category: "funny" },
    { content: "你知道我最喜欢什么时间吗？和你在一起的每一秒。", source: "网络流行", category: "funny" },
    { content: "你知道我最喜欢什么地方吗？你的怀抱。", source: "网络流行", category: "funny" },
    { content: "你知道我最喜欢什么礼物吗？你的微笑。", source: "网络流行", category: "funny" },
    { content: "你知道我最喜欢什么梦吗？和你一起做的梦。", source: "网络流行", category: "funny" },

    // 更多求婚类情话
    { content: "我想要和你一起建立一个温暖的家，你愿意成为我的妻子吗？", source: "网络流行", category: "proposal" },
    { content: "我想要和你一起度过余生的每一天，你愿意嫁给我吗？", source: "网络流行", category: "proposal" },
    { content: "我想要和你一起实现我们的梦想，你愿意和我结婚吗？", source: "网络流行", category: "proposal" },
    { content: "我想要和你一起看遍世界的美景，你愿意成为我的新娘吗？", source: "网络流行", category: "proposal" },
    { content: "我想要和你一起创造美好的回忆，你愿意嫁给我吗？", source: "网络流行", category: "proposal" },
    { content: "我想要和你一起面对未来的所有挑战，你愿意和我结婚吗？", source: "网络流行", category: "proposal" },
    { content: "我想要和你一起分享人生的所有喜怒哀乐，你愿意成为我的妻子吗？", source: "网络流行", category: "proposal" },
    { content: "我想要和你一起变老，一起看孙子孙女长大，你愿意嫁给我吗？", source: "网络流行", category: "proposal" },
    { content: "我想要和你一起走过人生的每一个春夏秋冬，你愿意和我结婚吗？", source: "网络流行", category: "proposal" },
    { content: "我想要和你一起创造属于我们的家，你愿意成为我的新娘吗？", source: "网络流行", category: "proposal" },

    // 更多纪念日类情话
    { content: "感谢时间让我们相遇，感谢爱情让我们相守。", source: "网络流行", category: "anniversary" },
    { content: "在这个特殊的日子里，我想对你说：遇见你是我最大的幸运。", source: "网络流行", category: "anniversary" },
    { content: "愿我们的爱情像美酒一样，越陈越香，越久越浓。", source: "网络流行", category: "anniversary" },
    { content: "感谢你陪伴我走过了这么多美好的时光，愿我们的爱情永远如初。", source: "网络流行", category: "anniversary" },
    { content: "在这个纪念日里，我想重新向你表白：我爱你，永远爱你。", source: "网络流行", category: "anniversary" },
    { content: "时间见证了我们的爱情，愿我们的爱情也见证时间的流逝。", source: "网络流行", category: "anniversary" },
    { content: "感谢你让我的生命如此精彩，愿我们的爱情永远美好。", source: "网络流行", category: "anniversary" },
    { content: "在这个特别的日子里，我想对你说：你是我生命中最重要的人。", source: "网络流行", category: "anniversary" },
    { content: "我们一起度过了这么多个春夏秋冬，愿我们的爱情四季如春。", source: "网络流行", category: "anniversary" },
    { content: "感谢你选择了我，也感谢我选择了你，愿我们的选择永远正确。", source: "网络流行", category: "anniversary" },

    // 更多通用类情话
    { content: "你是我见过最美的风景，也是我心中最美的回忆。", source: "网络流行", category: "general" },
    { content: "我想要和你一起看日出，然后告诉你，你比太阳还要温暖。", source: "网络流行", category: "general" },
    { content: "你是我心中的那首歌，每当想起你，我就会不自觉地哼唱。", source: "网络流行", category: "general" },
    { content: "我想要和你一起散步，走过每一条我们曾经走过的路。", source: "网络流行", category: "general" },
    { content: "你是我生命中的阳光，照亮了我前进的道路。", source: "网络流行", category: "general" },
    { content: "我想要和你一起喝咖啡，品尝生活的苦涩和甘甜。", source: "网络流行", category: "general" },
    { content: "你是我心中的那朵花，永远绽放在我的心田。", source: "网络流行", category: "general" },
    { content: "我想要和你一起看电影，在黑暗中感受彼此的温暖。", source: "网络流行", category: "general" },
    { content: "你是我生命中的彩虹，让我的世界充满色彩。", source: "网络流行", category: "general" },
    { content: "我想要和你一起旅行，去看我们从未见过的风景。", source: "网络流行", category: "general" }
];

// 导入数据函数
function importQuotes() {
    const db = new sqlite3.Database(dbPath);
    
    console.log('开始导入现代情话数据...');
    
    db.serialize(() => {
        const now = getCurrentTimestamp();
        let imported = 0;
        let errors = 0;
        
        additionalModernQuotes.forEach((item, index) => {
            const timestamp = now + index + 1000;
            const beijingDate = getBeijingDateString(timestamp);
            const beijingDateTime = getBeijingDateTimeString(timestamp);
            
            db.run(
                `INSERT INTO modern_love_quotes (content, source, category, language, created_timestamp, updated_timestamp, beijing_date, beijing_datetime, status, sort_order, popularity_score)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [item.content, item.source, item.category, 'zh', timestamp, timestamp, beijingDate, beijingDateTime, 'active', index + 1000, Math.floor(Math.random() * 100)],
                function(err) {
                    if (err) {
                        console.error(`导入第 ${index + 1} 条数据失败:`, err.message);
                        errors++;
                    } else {
                        imported++;
                    }
                    
                    // 最后一条数据处理完成后输出统计信息
                    if (index === additionalModernQuotes.length - 1) {
                        setTimeout(() => {
                            console.log(`\n导入完成！`);
                            console.log(`成功导入: ${imported} 条`);
                            console.log(`失败: ${errors} 条`);
                            console.log(`总计: ${additionalModernQuotes.length} 条`);
                            
                            // 查询总数
                            db.get("SELECT COUNT(*) as total FROM modern_love_quotes WHERE status = 'active'", (err, row) => {
                                if (!err) {
                                    console.log(`数据库中现有现代情话总数: ${row.total} 条`);
                                }
                                db.close();
                            });
                        }, 1000);
                    }
                }
            );
        });
    });
}

// 检查数据库是否存在
if (!fs.existsSync(dbPath)) {
    console.error('数据库文件不存在:', dbPath);
    console.log('请先启动服务器以创建数据库表结构');
    process.exit(1);
}

// 执行导入
importQuotes();
