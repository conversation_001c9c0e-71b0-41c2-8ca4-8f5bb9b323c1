#!/bin/bash

# H.265并行视频压缩进度监控脚本
# 支持四层架构和多进程并行监控

echo "🎬 H.265并行视频压缩进度监控 (四层架构)"
echo "========================================"

# 获取所有源视频文件信息
declare -A original_sizes
declare -A original_names

if [ -d "src/client/assets/videos" ]; then
    for video_dir in src/client/assets/videos/*/; do
        if [ -d "$video_dir" ]; then
            video_name=$(basename "$video_dir")
            video_file="$video_dir$video_name.mp4"
            if [ -f "$video_file" ]; then
                size=$(stat -c%s "$video_file" 2>/dev/null || echo "0")
                size_mb=$(awk "BEGIN {printf \"%.1f\", $size/1024/1024}" 2>/dev/null || echo "0")
                original_sizes["$video_name"]="$size"
                original_names["$video_name"]="$size_mb"
            fi
        fi
    done
fi

echo "📊 源视频文件信息:"
for name in "${!original_names[@]}"; do
    printf "  %-15s %8s MB\n" "$name" "${original_names[$name]}"
done
echo ""

while true; do
    clear
    echo "🎬 H.265视频压缩进度监控 (四层架构)"
    echo "========================================"
    
    # 显示源文件信息
    echo "📊 源视频文件:"
    for name in "${!original_names[@]}"; do
        printf "  %-15s %8s MB\n" "$name" "${original_names[$name]}"
    done
    echo ""
    
    # 检查进程状态 (支持并行监控)
    ffmpeg_count=$(ps aux | grep ffmpeg | grep libx265 | grep -v grep | wc -l)
    bash_nohup_count=$(ps aux | grep "bash -c.*compress_video" | grep -v grep | wc -l)

    # 系统资源信息
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1 2>/dev/null || echo "N/A")
    mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}' 2>/dev/null || echo "N/A")

    echo "🔄 活跃H.265压缩进程: $ffmpeg_count"
    echo "🔄 后台nohup任务: $bash_nohup_count"
    echo "💻 系统状态: CPU核心 $(nproc) | CPU使用率 ${cpu_usage}% | 内存使用率 ${mem_usage}%"

    # 显示详细进程信息
    if [ "$ffmpeg_count" -gt 0 ]; then
        echo ""
        echo "📋 并行压缩进程详情:"
        ps aux | grep ffmpeg | grep libx265 | grep -v grep | while read line; do
            pid=$(echo $line | awk '{print $2}')
            cpu=$(echo $line | awk '{print $3}')
            mem=$(echo $line | awk '{print $4}')
            time=$(echo $line | awk '{print $10}')

            # 提取输入文件名和处理阶段
            input_file=$(echo $line | grep -o 'src/client/assets/videos/[^[:space:]]*/[^[:space:]]*\.mp4' | head -1)
            pass_info=""

            if [ -n "$input_file" ]; then
                # 从输入文件路径提取文件名
                video_name=$(basename "$(dirname "$input_file")")

                # 判断是第一遍还是第二遍
                if echo "$line" | grep -q "pass 1"; then
                    pass_info="(第1遍分析)"
                elif echo "$line" | grep -q "pass 2"; then
                    pass_info="(第2遍编码)"
                else
                    pass_info="(处理中)"
                fi

                output_file="${video_name}.mp4 ${pass_info}"
            else
                output_file="unknown"
            fi

            echo "  PID: $pid | CPU: ${cpu}% | 内存: ${mem}% | 时间: $time | 文件: $output_file"
        done
    fi

    if [ "$ffmpeg_count" -gt 0 ]; then
        echo "📹 当前压缩任务详情:"
        ps aux | grep ffmpeg | grep libx265 | grep -v grep | while read line; do
            pid=$(echo $line | awk '{print $2}')
            input_file=$(echo $line | grep -o 'src/client/assets/videos/[^[:space:]]*/[^[:space:]]*\.mp4' | head -1)

            if [ -n "$input_file" ]; then
                video_name=$(basename "$(dirname "$input_file")")
                echo "  进程ID: $pid | 正在处理: ${video_name}.mp4"
            else
                echo "  进程ID: $pid | 处理文件: unknown"
            fi
        done
    fi
    echo ""
    
    # 显示压缩进度 (更新为统一路径)
    echo "📁 压缩进度 (src/client/assets/video-compressed/):"
    if [ -d "src/client/assets/video-compressed" ]; then
        for file in src/client/assets/video-compressed/*.mp4; do
            if [ -f "$file" ]; then
                filename=$(basename "$file" .mp4)
                size=$(stat -c%s "$file" 2>/dev/null || echo "0")
                size_mb=$(awk "BEGIN {printf \"%.1f\", $size/1024/1024}" 2>/dev/null || echo "0")
                
                # 计算压缩率
                if [ "${original_sizes[$filename]}" ] && [ "${original_sizes[$filename]}" -gt 0 ]; then
                    original_size="${original_sizes[$filename]}"
                    compression_ratio=$(awk "BEGIN {printf \"%.1f\", (1 - $size / $original_size) * 100}" 2>/dev/null || echo "0")
                    printf "  %-15s %8s MB (压缩率: %5s%%) ✅\n" "$filename" "$size_mb" "$compression_ratio"
                else
                    printf "  %-15s %8s MB (新文件) ✅\n" "$filename" "$size_mb"
                fi
            fi
        done
    else
        echo "  压缩目录尚未创建..."
    fi
    
    echo ""
    echo "📤 统一存储状态:"

    # 统一压缩目录 (更新路径)
    echo "  📁 压缩视频目录 (src/client/assets/video-compressed/):"
    if [ -d "src/client/assets/video-compressed" ]; then
        compressed_count=$(ls -1 src/client/assets/video-compressed/*.mp4 2>/dev/null | wc -l)
        echo "    完成文件数: $compressed_count"
        if [ "$compressed_count" -gt 0 ]; then
            ls -1 src/client/assets/video-compressed/*.mp4 2>/dev/null | while read file; do
                filename=$(basename "$file")
                size=$(stat -c%s "$file" 2>/dev/null || echo "0")
                size_mb=$(awk "BEGIN {printf \"%.1f\", $size/1024/1024}" 2>/dev/null || echo "0")
                printf "      %-15s %8s MB\n" "$filename" "$size_mb"
            done
        fi
    else
        echo "    目录未创建"
    fi

    # 显示nohup日志状态
    echo ""
    echo "📝 nohup日志文件状态:"
    for log_file in nohup_*.log; do
        if [ -f "$log_file" ]; then
            log_size=$(stat -c%s "$log_file" 2>/dev/null || echo "0")
            log_size_kb=$(awk "BEGIN {printf \"%.1f\", $log_size/1024}" 2>/dev/null || echo "0")

            # 从文件名提取视频名称
            video_name=$(echo "$log_file" | sed 's/nohup_//; s/\.log$//')

            echo "  📄 $video_name: ${log_size_kb}KB"

            # 显示最后几行日志和进度分析
            if [ "$log_size" -gt 0 ]; then
                # 获取最新的frame信息
                latest_frame=$(tail -5 "$log_file" 2>/dev/null | grep -o 'frame=[[:space:]]*[0-9]*' | tail -1 | grep -o '[0-9]*')
                latest_speed=$(tail -5 "$log_file" 2>/dev/null | grep -o 'speed=[[:space:]]*[0-9.]*x' | tail -1 | grep -o '[0-9.]*')

                # 检查是否完成
                if tail -5 "$log_file" 2>/dev/null | grep -q "文件已保存到统一路径"; then
                    echo "    状态: ✅ 压缩完成"
                elif tail -5 "$log_file" 2>/dev/null | grep -q "第一遍.*分析"; then
                    echo "    状态: 🔍 第一遍分析中 | 帧数: ${latest_frame:-N/A} | 速度: ${latest_speed:-N/A}x"
                elif tail -5 "$log_file" 2>/dev/null | grep -q "第二遍.*编码"; then
                    echo "    状态: 🎬 第二遍编码中 | 帧数: ${latest_frame:-N/A} | 速度: ${latest_speed:-N/A}x"
                else
                    echo "    状态: 🔄 处理中 | 帧数: ${latest_frame:-N/A} | 速度: ${latest_speed:-N/A}x"
                fi
            else
                echo "    状态: ⏳ 等待启动..."
            fi
        fi
    done

    echo ""
    echo "⏱️  $(date '+%H:%M:%S') - 每5秒更新一次 (Ctrl+C 退出)"
    echo "💡 提示: 即使关闭VSCode连接，nohup任务也会继续运行"

    # 如果没有活跃进程，显示完成信息
    if [ "$ffmpeg_count" -eq 0 ] && [ "$bash_nohup_count" -eq 0 ]; then
        echo ""
        echo "✅ 所有H.265并行压缩任务已完成！"
        echo ""
        echo "🎯 H.265并行压缩总结："
        echo "  • 编码器: libx265 (H.265/HEVC)"
        echo "  • 并行度: 最多2个任务同时运行"
        echo "  • 质量设置: CRF 14/16, 2-Pass VBR"
        echo "  • 预设: slow (高质量)"
        echo "  • 音频: 无音频处理"
        echo "  • 智能策略: 小文件直接复制"
        echo "  • 后台运行: 支持nohup，断开连接不影响"
        echo ""
        echo "📁 文件位置:"
        echo "  • 统一存储: src/client/assets/video-compressed/"
        echo ""
        echo "🚀 可以开始上传到四层CDN架构了！"
        break
    fi
    
    sleep 5
done
