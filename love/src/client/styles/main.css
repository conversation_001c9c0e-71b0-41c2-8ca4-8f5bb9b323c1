/**
 * Love项目主样式文件
 * 整合所有CSS样式，统一管理
 */

/* 导入原有样式文件 */
@import url('./style.css');
@import url('./pages.css');

/* 导入星空背景保障机制样式 */
@import url('./starry-background.css');

/* 新增的统一样式管理 */

/* 字体路径更新 - 适配新的资源结构 */
@font-face {
    font-family: 'Courgette';
    src: url('../assets/fonts/Courgette-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Great Vibes';
    src: url('../assets/fonts/GreatVibes-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'ZiXiaoHunGouYu';
    src: url('../assets/fonts/字小魂勾玉行书(商用需授权).ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'ZiXiaoHunSanFen';
    src: url('../assets/fonts/字小魂三分行楷(商用需授权).ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'ZiHunXingYun';
    src: url('../assets/fonts/字魂行云飞白体(商用需授权).ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

/* 背景视频容器样式 */
.video-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    object-fit: cover;
}

/* 视频背景样式 - 注意：视频路径通过HTML的src属性设置，不在CSS中 */
.video-background video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 页面特定的视频背景类（用于JavaScript控制） */
.home-video-bg,
.together-days-video-bg,
.anniversary-video-bg,
.meetings-video-bg,
.memorial-video-bg {
    /* 这些类用于JavaScript动态设置视频源 */
}

/* 响应式设计优化 */
@media (max-width: 768px) {
    .video-background {
        display: none; /* 移动端隐藏视频背景以提升性能 */
    }
}

/* 性能优化 */
.lazy-load {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.lazy-load.loaded {
    opacity: 1;
}

/* 无障碍访问优化 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 打印样式 */
@media print {
    .video-background,
    .navigation,
    .floating-elements {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}
