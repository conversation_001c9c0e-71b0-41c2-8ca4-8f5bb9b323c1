/**
 * 星空背景保障机制样式
 * 第四层CDN降级保障 - 当所有视频CDN失效时的美观背景
 * 
 * 特性：
 * - 统一的星空主题背景
 * - 各页面保持独特的主题色彩
 * - 流畅的动画效果
 * - 响应式设计
 * 
 * @version 1.0.0
 * <AUTHOR> Project Team
 */

/* ===== 基础星空背景 ===== */
.starry-background {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    background: linear-gradient(135deg, #0c0c2e 0%, #1a1a3e 25%, #2d1b69 50%, #4a148c 75%, #6a1b9a 100%);
}

/* 星空动画层 */
.starry-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, #eee, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, #fff, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: starryMove 20s linear infinite;
    opacity: 0.8;
}

/* 星空闪烁动画 */
.starry-background::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(1px 1px at 50px 50px, rgba(255,255,255,0.9), transparent),
        radial-gradient(1px 1px at 100px 100px, rgba(255,255,255,0.7), transparent),
        radial-gradient(1px 1px at 150px 150px, rgba(255,255,255,0.5), transparent);
    background-repeat: repeat;
    background-size: 300px 300px;
    animation: starryTwinkle 3s ease-in-out infinite alternate;
    opacity: 0.6;
}

/* ===== 页面主题星空背景 ===== */

/* 首页 - 粉色浪漫星空 */
.starry-background.home {
    background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 25%, #f9a8d4 50%, #f472b6 75%, #ec4899 100%);
}

.starry-background.home::before {
    background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.9), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,182,193,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255,105,180,0.7), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(255,182,193,0.8), transparent);
}

/* 纪念日 - 金色温暖星空 */
.starry-background.anniversary {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 25%, #fbbf24 50%, #f59e0b 75%, #d97706 100%);
}

.starry-background.anniversary::before {
    background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.9), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,215,0,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255,165,0,0.7), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(255,215,0,0.8), transparent);
}

/* 相遇记录 - 深紫神秘星空 */
.starry-background.meetings {
    background: linear-gradient(135deg, #0c0c2e 0%, #1a1a3e 25%, #2d1b69 50%, #4a148c 75%, #6a1b9a 100%);
}

.starry-background.meetings::before {
    background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.9), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(138,43,226,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(75,0,130,0.7), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(138,43,226,0.8), transparent);
}

/* 纪念页面 - 蓝色海洋星空 */
.starry-background.memorial {
    background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 25%, #4fc3f7 50%, #29b6f6 75%, #0288d1 100%);
}

.starry-background.memorial::before {
    background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.9), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(135,206,235,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(0,191,255,0.7), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(135,206,235,0.8), transparent);
}

/* 在一起的日子 - 橙色夕阳星空 */
.starry-background.together-days {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 25%, #ffcc02 50%, #ff9800 75%, #f57c00 100%);
}

.starry-background.together-days::before {
    background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.9), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,165,0,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255,140,0,0.7), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(255,165,0,0.8), transparent);
}

/* ===== 动画定义 ===== */

/* 星空移动动画 */
@keyframes starryMove {
    0% {
        transform: translateX(0) translateY(0);
    }
    100% {
        transform: translateX(-200px) translateY(-100px);
    }
}

/* 星空闪烁动画 */
@keyframes starryTwinkle {
    0% {
        opacity: 0.3;
    }
    100% {
        opacity: 0.8;
    }
}

/* 星空渐入动画 */
@keyframes starryFadeIn {
    0% {
        opacity: 0;
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* ===== 响应式设计 ===== */

/* 移动设备优化 */
@media (max-width: 768px) {
    .starry-background::before {
        background-size: 150px 75px;
        animation-duration: 15s;
    }
    
    .starry-background::after {
        background-size: 200px 200px;
        animation-duration: 2s;
    }
}

/* 高分辨率设备优化 */
@media (min-width: 1920px) {
    .starry-background::before {
        background-size: 300px 150px;
        animation-duration: 25s;
    }
    
    .starry-background::after {
        background-size: 400px 400px;
        animation-duration: 4s;
    }
}

/* ===== 视频容器集成样式 ===== */

/* 当视频加载失败时，视频容器应用星空背景 */
.video-background.starry-fallback {
    position: relative;
}

.video-background.starry-fallback::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    animation: starryFadeIn 1s ease-out;
}

/* 确保内容在星空背景之上 */
.video-background.starry-fallback > * {
    position: relative;
    z-index: 2;
}

/* ===== 加载状态指示 ===== */

/* 星空背景加载指示器 */
.starry-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    text-align: center;
    z-index: 10;
    animation: starryTwinkle 2s ease-in-out infinite alternate;
}

.starry-loading::before {
    content: '✨';
    display: block;
    font-size: 24px;
    margin-bottom: 8px;
    animation: starryMove 3s linear infinite;
}

/* ===== 性能优化 ===== */

/* 减少重绘和回流 */
.starry-background,
.starry-background::before,
.starry-background::after {
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
}

/* GPU加速 */
.starry-background {
    transform: translateZ(0);
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
    .starry-background {
        filter: brightness(0.9);
    }
}

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
    .starry-background::before,
    .starry-background::after {
        animation: none;
    }
    
    .starry-background::before {
        opacity: 0.6;
    }
    
    .starry-background::after {
        opacity: 0.4;
    }
}
