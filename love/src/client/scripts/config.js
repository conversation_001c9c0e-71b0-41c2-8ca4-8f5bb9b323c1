/**
 * Love项目前端配置
 * 从后端API获取.env配置，实现前后端配置统一
 */

// 前端配置对象
window.LOVE_CONFIG = null;

// 从后端API获取配置
async function loadConfig() {
    try {
        const response = await fetch('/api/config');
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                // 使用后端返回的配置
                window.LOVE_CONFIG = {
                    DOMAIN: result.data.domain.base,
                    BASE_URL: result.data.domain.url,
                    API_URL: result.data.api.baseUrl,

                    // 页面路径配置
                    PAGES: {
                        HOME: '/',
                        TOGETHER_DAYS: '/together-days',
                        ANNIVERSARY: '/anniversary',
                        MEETINGS: '/meetings',
                        MEMORIAL: '/memorial'
                    }
                };

                console.log('✅ 前端配置已从后端加载:', window.LOVE_CONFIG);
                return window.LOVE_CONFIG;
            }
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    } catch (error) {
        console.error('❌ 无法获取配置，网站无法正常工作:', error.message);
        console.error('请检查后端服务是否正常运行');

        // 不提供fallback，确保配置统一性
        window.LOVE_CONFIG = null;
        throw new Error('配置加载失败，请刷新页面重试');
    }
}

// 便捷方法
window.getPageUrl = function(page) {
    if (!window.LOVE_CONFIG) {
        throw new Error('配置未加载，请先调用 loadConfig()');
    }
    return window.LOVE_CONFIG.BASE_URL + (window.LOVE_CONFIG.PAGES[page] || page);
};

window.getHomeUrl = function() {
    if (!window.LOVE_CONFIG) {
        throw new Error('配置未加载，请先调用 loadConfig()');
    }
    return window.LOVE_CONFIG.BASE_URL + '/';
};

// 自动加载配置
document.addEventListener('DOMContentLoaded', function() {
    loadConfig().then(config => {
        // 触发配置加载完成事件
        const event = new CustomEvent('loveConfigLoaded', {
            detail: { config: config }
        });
        document.dispatchEvent(event);
    });
});

console.log('📦 Love前端配置模块已加载，将从后端API获取配置');