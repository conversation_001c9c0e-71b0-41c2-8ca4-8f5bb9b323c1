const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 引入配置管理模块
const config = require('../../../config/config');

// Database setup
const dataDir = path.join(__dirname, '../../../data');
const dbPath = config.database.path;

// 确保data目录存在
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

// 创建数据库连接
const db = new sqlite3.Database(dbPath);

// 北京时间工具函数
function getBeijingTime(timestamp) {
    const date = new Date(timestamp * 1000);
    // 使用正确的时区转换方法
    return new Date(date.toLocaleString("en-US", {timeZone: "Asia/Shanghai"}));
}

function getBeijingDateString(timestamp) {
    const beijingTime = getBeijingTime(timestamp);
    return beijingTime.toISOString().split('T')[0];
}

function getBeijingDateTimeString(timestamp) {
    const beijingTime = getBeijingTime(timestamp);
    return beijingTime.toISOString().replace('T', ' ').split('.')[0];
}

// Helper function to get current timestamp
const getCurrentTimestamp = () => Math.floor(Date.now() / 1000);

// Helper function to format timestamp for frontend
const formatTimestamp = (timestamp) => {
    return new Date(timestamp * 1000).toISOString();
};

// 检查使用哪个表
function getTableName(callback) {
    db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='love_messages_new'", (err, row) => {
        if (err) {
            callback('love_messages'); // 默认使用旧表
        } else {
            callback(row ? 'love_messages_new' : 'love_messages');
        }
    });
}

// 初始化数据库表结构
function initializeDatabase() {
    db.serialize(() => {
        // 检查是否为新的数据库结构
        db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='love_messages_new'", (err, row) => {
            if (!row) {
                // 如果没有新表，创建旧表结构（兼容性）
                db.run(`CREATE TABLE IF NOT EXISTS love_messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    author TEXT NOT NULL CHECK(author IN ('Yu', 'Wang', 'Other')),
                    content TEXT NOT NULL,
                    created_at INTEGER NOT NULL,
                    updated_at INTEGER NOT NULL,
                    ip TEXT DEFAULT ''
                )`);

                // Create index for better performance
                db.run(`CREATE INDEX IF NOT EXISTS idx_created_at ON love_messages(created_at)`);
            }
        });

        // 创建时光轴表 (love_timeline)
        db.run(`CREATE TABLE IF NOT EXISTS love_timeline (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            title TEXT NOT NULL CHECK(length(title) > 0 AND length(title) <= 200),
            description TEXT NOT NULL CHECK(length(description) > 0 AND length(description) <= 2000),
            created_timestamp INTEGER NOT NULL,
            updated_timestamp INTEGER NOT NULL,
            beijing_date TEXT NOT NULL,
            beijing_datetime TEXT NOT NULL,
            status TEXT DEFAULT 'active' CHECK(status IN ('active', 'deleted')),
            sort_order INTEGER DEFAULT 0,
            CHECK(created_timestamp > 0),
            CHECK(updated_timestamp >= created_timestamp)
        )`);

        // 创建美好瞬间表 (love_memories)
        db.run(`CREATE TABLE IF NOT EXISTS love_memories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            icon TEXT NOT NULL CHECK(length(icon) > 0 AND length(icon) <= 100),
            title TEXT NOT NULL CHECK(length(title) > 0 AND length(title) <= 200),
            content TEXT NOT NULL CHECK(length(content) > 0 AND length(content) <= 2000),
            created_timestamp INTEGER NOT NULL,
            updated_timestamp INTEGER NOT NULL,
            beijing_date TEXT NOT NULL,
            beijing_datetime TEXT NOT NULL,
            status TEXT DEFAULT 'active' CHECK(status IN ('active', 'deleted')),
            sort_order INTEGER DEFAULT 0,
            CHECK(created_timestamp > 0),
            CHECK(updated_timestamp >= created_timestamp)
        )`);

        // 创建现代情话表 (modern_love_quotes)
        db.run(`CREATE TABLE IF NOT EXISTS modern_love_quotes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            content TEXT NOT NULL CHECK(length(content) > 0 AND length(content) <= 500),
            source TEXT DEFAULT '' CHECK(length(source) <= 200),
            category TEXT DEFAULT 'general' CHECK(category IN ('romantic', 'sweet', 'deep', 'funny', 'proposal', 'anniversary', 'general')),
            language TEXT DEFAULT 'zh' CHECK(language IN ('zh', 'en')),
            created_timestamp INTEGER NOT NULL,
            updated_timestamp INTEGER NOT NULL,
            beijing_date TEXT NOT NULL,
            beijing_datetime TEXT NOT NULL,
            status TEXT DEFAULT 'active' CHECK(status IN ('active', 'deleted')),
            sort_order INTEGER DEFAULT 0,
            tags TEXT DEFAULT '',
            popularity_score INTEGER DEFAULT 0,
            CHECK(created_timestamp > 0),
            CHECK(updated_timestamp >= created_timestamp)
        )`);

        // 创建索引以提高查询性能
        db.run(`CREATE INDEX IF NOT EXISTS idx_timeline_created_timestamp ON love_timeline(created_timestamp)`);
        db.run(`CREATE INDEX IF NOT EXISTS idx_timeline_status ON love_timeline(status)`);
        db.run(`CREATE INDEX IF NOT EXISTS idx_timeline_sort_order ON love_timeline(sort_order)`);

        db.run(`CREATE INDEX IF NOT EXISTS idx_memories_created_timestamp ON love_memories(created_timestamp)`);
        db.run(`CREATE INDEX IF NOT EXISTS idx_memories_status ON love_memories(status)`);
        db.run(`CREATE INDEX IF NOT EXISTS idx_memories_sort_order ON love_memories(sort_order)`);

        // 现代情话表索引
        db.run(`CREATE INDEX IF NOT EXISTS idx_modern_quotes_created_timestamp ON modern_love_quotes(created_timestamp)`);
        db.run(`CREATE INDEX IF NOT EXISTS idx_modern_quotes_status ON modern_love_quotes(status)`);
        db.run(`CREATE INDEX IF NOT EXISTS idx_modern_quotes_category ON modern_love_quotes(category)`);
        db.run(`CREATE INDEX IF NOT EXISTS idx_modern_quotes_popularity ON modern_love_quotes(popularity_score)`);

        // 初始化数据
        setTimeout(() => {
            const { initializeAllData } = require('./dataInit');
            initializeAllData();
        }, 1000);
    });
}

module.exports = {
    db,
    getBeijingTime,
    getBeijingDateString,
    getBeijingDateTimeString,
    getCurrentTimestamp,
    formatTimestamp,
    getTableName,
    initializeDatabase
};
