/**
 * 时间处理工具模块
 * 提供北京时间转换和格式化功能
 */

// 北京时间工具函数
function getBeijingTime(timestamp) {
    const date = new Date(timestamp * 1000);
    // 使用正确的时区转换方法
    return new Date(date.toLocaleString("en-US", {timeZone: "Asia/Shanghai"}));
}

function getBeijingDateString(timestamp) {
    const beijingTime = getBeijingTime(timestamp);
    return beijingTime.toISOString().split('T')[0];
}

function getBeijingDateTimeString(timestamp) {
    const beijingTime = getBeijingTime(timestamp);
    return beijingTime.toISOString().replace('T', ' ').split('.')[0];
}

// Helper function to get current timestamp
const getCurrentTimestamp = () => Math.floor(Date.now() / 1000);

// Helper function to format timestamp for frontend
const formatTimestamp = (timestamp) => {
    return new Date(timestamp * 1000).toISOString();
};

// 格式化日期为中文格式
function formatChineseDate(timestamp) {
    const date = getBeijingTime(timestamp);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}年${month}月${day}日`;
}

// 格式化时间为中文格式
function formatChineseDateTime(timestamp) {
    const date = getBeijingTime(timestamp);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();
    return `${year}年${month}月${day}日 ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
}

// 计算时间差
function getTimeDifference(startTimestamp, endTimestamp = null) {
    const end = endTimestamp || getCurrentTimestamp();
    const diff = end - startTimestamp;
    
    const days = Math.floor(diff / (24 * 3600));
    const hours = Math.floor((diff % (24 * 3600)) / 3600);
    const minutes = Math.floor((diff % 3600) / 60);
    
    return {
        days,
        hours,
        minutes,
        totalSeconds: diff
    };
}

// 获取相对时间描述
function getRelativeTime(timestamp) {
    const now = getCurrentTimestamp();
    const diff = now - timestamp;
    
    if (diff < 60) {
        return '刚刚';
    } else if (diff < 3600) {
        const minutes = Math.floor(diff / 60);
        return `${minutes}分钟前`;
    } else if (diff < 86400) {
        const hours = Math.floor(diff / 3600);
        return `${hours}小时前`;
    } else if (diff < 2592000) {
        const days = Math.floor(diff / 86400);
        return `${days}天前`;
    } else {
        return formatChineseDate(timestamp);
    }
}

module.exports = {
    getBeijingTime,
    getBeijingDateString,
    getBeijingDateTimeString,
    getCurrentTimestamp,
    formatTimestamp,
    formatChineseDate,
    formatChineseDateTime,
    getTimeDifference,
    getRelativeTime
};
