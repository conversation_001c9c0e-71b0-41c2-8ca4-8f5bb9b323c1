/**
 * 留言API路由
 * 处理留言相关的所有API请求
 */

const express = require('express');
const router = express.Router();
const Message = require('../models/Message');
const { asyncHandler, ValidationError } = require('../middleware/error');

// Get all messages
router.get('/', asyncHandler(async (req, res) => {
    Message.getAll((err, messages) => {
        if (err) {
            console.error('Error fetching messages:', err);
            return res.json({
                success: false,
                message: '获取留言失败: ' + err.message
            });
        }

        res.json({
            success: true,
            message: '',
            data: messages
        });
    });
}));

// Get messages with pagination
router.get('/paginated', asyncHandler(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const pageSize = Math.min(parseInt(req.query.page_size) || 20, 100);
    
    Message.getPaginated(page, pageSize, (err, result) => {
        if (err) {
            console.error('Error fetching paginated messages:', err);
            return res.json({
                success: false,
                message: '获取留言失败: ' + err.message
            });
        }
        
        res.json({
            success: true,
            message: '',
            data: result
        });
    });
}));

// Create new message
router.post('/', asyncHandler(async (req, res) => {
    const { author, content } = req.body;
    const clientIP = req.ip || req.connection.remoteAddress || '';
    
    Message.create({ author, content }, clientIP, (err, newMessage) => {
        if (err) {
            console.error('Error creating message:', err);
            return res.status(400).json({
                success: false,
                message: err.message
            });
        }

        res.json({
            success: true,
            message: '留言创建成功',
            data: newMessage
        });
    });
}));

// Update message
router.put('/:id', asyncHandler(async (req, res) => {
    const messageId = parseInt(req.params.id);
    const { content } = req.body;
    
    if (!content) {
        return res.status(400).json({
            success: false,
            message: '内容不能为空'
        });
    }
    
    Message.update(messageId, content, (err, result) => {
        if (err) {
            console.error('Error updating message:', err);
            const statusCode = err.message === '留言不存在' ? 404 : 500;
            return res.status(statusCode).json({
                success: false,
                message: err.message
            });
        }

        res.json({
            success: true,
            message: '留言更新成功'
        });
    });
}));

// Delete message
router.delete('/:id', asyncHandler(async (req, res) => {
    const messageId = parseInt(req.params.id);

    Message.delete(messageId, (err, result) => {
        if (err) {
            console.error('Error deleting message:', err);
            const statusCode = err.message === '留言不存在' ? 404 : 500;
            return res.status(statusCode).json({
                success: false,
                message: err.message
            });
        }

        res.json({
            success: true,
            message: '留言删除成功'
        });
    });
}));

// Get message statistics
router.get('/stats', asyncHandler(async (req, res) => {
    Message.getStats((err, stats) => {
        if (err) {
            console.error('Error getting stats:', err);
            return res.json({
                success: false,
                message: '获取统计信息失败: ' + err.message
            });
        }
        
        res.json({
            success: true,
            message: '',
            data: stats
        });
    });
}));

module.exports = router;
