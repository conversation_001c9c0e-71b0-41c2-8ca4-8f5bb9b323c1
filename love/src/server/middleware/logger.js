/**
 * 日志中间件
 * 记录请求日志
 */

const fs = require('fs');
const path = require('path');
const { getCurrentTimestamp, getBeijingDateTimeString } = require('../utils/datetime');

// 日志文件路径
const logDir = path.join(__dirname, '../../../logs');
const logFile = path.join(logDir, 'backend.log');

// 确保日志目录存在
if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
}

// 写入日志文件
function writeLog(message) {
    const timestamp = getCurrentTimestamp();
    const beijingTime = getBeijingDateTimeString(timestamp);
    const logEntry = `[${beijingTime}] ${message}\n`;
    
    fs.appendFile(logFile, logEntry, (err) => {
        if (err) {
            console.error('Error writing to log file:', err);
        }
    });
}

// 请求日志中间件
function requestLogger(req, res, next) {
    const start = Date.now();
    const { method, url, ip } = req;
    const userAgent = req.get('User-Agent') || '';
    
    // 记录请求开始
    const requestInfo = `${method} ${url} - IP: ${ip} - User-Agent: ${userAgent}`;
    writeLog(`REQUEST START: ${requestInfo}`);
    console.log(`[${new Date().toISOString()}] ${requestInfo}`);
    
    // 监听响应结束
    res.on('finish', () => {
        const duration = Date.now() - start;
        const { statusCode } = res;
        const responseInfo = `${method} ${url} - ${statusCode} - ${duration}ms`;
        writeLog(`REQUEST END: ${responseInfo}`);
        console.log(`[${new Date().toISOString()}] ${responseInfo}`);
    });
    
    next();
}

// 错误日志记录
function logError(error, req = null) {
    const timestamp = getCurrentTimestamp();
    const beijingTime = getBeijingDateTimeString(timestamp);
    
    let errorMessage = `ERROR: ${error.message}`;
    if (error.stack) {
        errorMessage += `\nStack: ${error.stack}`;
    }
    
    if (req) {
        errorMessage += `\nRequest: ${req.method} ${req.url}`;
        errorMessage += `\nIP: ${req.ip}`;
    }
    
    const logEntry = `[${beijingTime}] ${errorMessage}\n`;
    
    fs.appendFile(logFile, logEntry, (err) => {
        if (err) {
            console.error('Error writing error log:', err);
        }
    });
    
    console.error(`[${new Date().toISOString()}] ${errorMessage}`);
}

// 信息日志记录
function logInfo(message) {
    const timestamp = getCurrentTimestamp();
    const beijingTime = getBeijingDateTimeString(timestamp);
    const logEntry = `[${beijingTime}] INFO: ${message}\n`;
    
    fs.appendFile(logFile, logEntry, (err) => {
        if (err) {
            console.error('Error writing info log:', err);
        }
    });
    
    console.log(`[${new Date().toISOString()}] INFO: ${message}`);
}

module.exports = {
    requestLogger,
    logError,
    logInfo,
    writeLog
};
