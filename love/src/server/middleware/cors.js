/**
 * CORS中间件配置
 * 处理跨域请求
 */

const cors = require('cors');
const config = require('../../../config/config');

// CORS配置选项
const corsOptions = {
    origin: config.server.corsOrigin,
    credentials: true,
    optionsSuccessStatus: 200,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

// 创建CORS中间件
const corsMiddleware = cors(corsOptions);

module.exports = corsMiddleware;
