/**
 * 错误处理中间件
 * 统一处理应用程序错误
 */

const { logError } = require('./logger');

// 404错误处理
function notFoundHandler(req, res, next) {
    const error = new Error(`Not Found - ${req.originalUrl}`);
    error.status = 404;
    next(error);
}

// 全局错误处理中间件
function errorHandler(err, req, res, next) {
    // 记录错误日志
    logError(err, req);
    
    // 设置默认错误状态码
    const statusCode = err.status || err.statusCode || 500;
    
    // 开发环境显示详细错误信息，生产环境隐藏
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    // 构建错误响应
    const errorResponse = {
        success: false,
        message: err.message || '服务器内部错误',
        status: statusCode
    };
    
    // 开发环境添加堆栈信息
    if (isDevelopment && err.stack) {
        errorResponse.stack = err.stack;
    }
    
    // 特定错误类型的处理
    if (err.name === 'ValidationError') {
        errorResponse.message = '数据验证失败';
        errorResponse.details = err.details;
    } else if (err.name === 'CastError') {
        errorResponse.message = '无效的数据格式';
    } else if (err.code === 'SQLITE_CONSTRAINT') {
        errorResponse.message = '数据约束违反';
    } else if (err.code === 'ENOENT') {
        errorResponse.message = '文件或资源不存在';
    }
    
    // 发送错误响应
    res.status(statusCode).json(errorResponse);
}

// 异步错误包装器
function asyncHandler(fn) {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
}

// 创建自定义错误
class AppError extends Error {
    constructor(message, statusCode = 500) {
        super(message);
        this.statusCode = statusCode;
        this.status = statusCode;
        this.isOperational = true;
        
        Error.captureStackTrace(this, this.constructor);
    }
}

// 验证错误
class ValidationError extends AppError {
    constructor(message, details = null) {
        super(message, 400);
        this.name = 'ValidationError';
        this.details = details;
    }
}

// 未找到错误
class NotFoundError extends AppError {
    constructor(message = '资源未找到') {
        super(message, 404);
        this.name = 'NotFoundError';
    }
}

// 未授权错误
class UnauthorizedError extends AppError {
    constructor(message = '未授权访问') {
        super(message, 401);
        this.name = 'UnauthorizedError';
    }
}

// 禁止访问错误
class ForbiddenError extends AppError {
    constructor(message = '禁止访问') {
        super(message, 403);
        this.name = 'ForbiddenError';
    }
}

module.exports = {
    notFoundHandler,
    errorHandler,
    asyncHandler,
    AppError,
    ValidationError,
    NotFoundError,
    UnauthorizedError,
    ForbiddenError
};
