/**
 * 留言数据模型
 * 处理留言的数据库操作
 */

const { db, getTableName, getCurrentTimestamp, formatTimestamp, getBeijingDateString, getBeijingDateTimeString } = require('../utils/database');

class Message {
    constructor(data) {
        this.id = data.id;
        this.author = data.author;
        this.content = data.content;
        this.created_at = data.created_at || data.created_timestamp;
        this.updated_at = data.updated_at || data.updated_timestamp;
        this.ip = data.ip || data.client_ip;
        this.beijing_date = data.beijing_date;
        this.beijing_datetime = data.beijing_datetime;
        this.status = data.status;
        this.version = data.version;
    }

    // 验证留言数据
    static validate(data) {
        const { author, content } = data;
        
        if (!author || !content) {
            return { valid: false, message: '作者和内容不能为空' };
        }
        
        if (!['Yu', 'Wang', 'Other'].includes(author)) {
            return { valid: false, message: '作者字段只能是"Yu"、"Wang"或"Other"' };
        }
        
        const trimmedContent = content.trim();
        if (trimmedContent.length === 0) {
            return { valid: false, message: '留言内容不能为空' };
        }
        
        if (trimmedContent.length > 1000) {
            return { valid: false, message: '留言内容不能超过1000个字符' };
        }
        
        return { valid: true, content: trimmedContent };
    }

    // 获取所有留言
    static getAll(callback) {
        getTableName((tableName) => {
            const query = tableName === 'love_messages_new'
                ? 'SELECT * FROM love_messages_new WHERE status = "active" ORDER BY created_timestamp DESC'
                : 'SELECT * FROM love_messages ORDER BY created_at DESC';

            db.all(query, [], (err, rows) => {
                if (err) {
                    return callback(err, null);
                }

                // Format messages for frontend
                const messages = rows.map(row => {
                    if (tableName === 'love_messages_new') {
                        return {
                            id: row.id,
                            author: row.author,
                            content: row.content,
                            created_at: row.created_timestamp,
                            timestamp: formatTimestamp(row.created_timestamp),
                            beijing_date: row.beijing_date,
                            beijing_datetime: row.beijing_datetime
                        };
                    } else {
                        return {
                            id: row.id,
                            author: row.author,
                            content: row.content,
                            created_at: row.created_at,
                            timestamp: formatTimestamp(row.created_at)
                        };
                    }
                });

                callback(null, messages);
            });
        });
    }

    // 分页获取留言
    static getPaginated(page = 1, pageSize = 20, callback) {
        const offset = (page - 1) * pageSize;
        
        getTableName((tableName) => {
            // Get total count
            const countQuery = tableName === 'love_messages_new'
                ? 'SELECT COUNT(*) as total FROM love_messages_new WHERE status = "active"'
                : 'SELECT COUNT(*) as total FROM love_messages';

            db.get(countQuery, [], (err, countRow) => {
                if (err) {
                    return callback(err, null);
                }
                
                const total = countRow.total;
                
                // Get paginated messages
                const query = tableName === 'love_messages_new'
                    ? 'SELECT * FROM love_messages_new WHERE status = "active" ORDER BY created_timestamp DESC LIMIT ? OFFSET ?'
                    : 'SELECT * FROM love_messages ORDER BY created_at DESC LIMIT ? OFFSET ?';

                db.all(query, [pageSize, offset], (err, rows) => {
                    if (err) {
                        return callback(err, null);
                    }
                    
                    // Format messages for frontend
                    const messages = rows.map(row => ({
                        id: row.id,
                        author: row.author,
                        content: row.content,
                        created_at: row.created_at || row.created_timestamp,
                        timestamp: formatTimestamp(row.created_at || row.created_timestamp)
                    }));
                    
                    callback(null, {
                        items: messages,
                        total: total,
                        page: page,
                        page_size: pageSize
                    });
                });
            });
        });
    }

    // 创建新留言
    static create(data, clientIP, callback) {
        const validation = Message.validate(data);
        if (!validation.valid) {
            return callback(new Error(validation.message), null);
        }

        const { author } = data;
        const content = validation.content;
        const now = getCurrentTimestamp();

        getTableName((tableName) => {
            if (tableName === 'love_messages_new') {
                // 使用新表结构
                const beijingDate = getBeijingDateString(now);
                const beijingDateTime = getBeijingDateTimeString(now);

                db.run(
                    `INSERT INTO love_messages_new
                    (author, content, created_timestamp, updated_timestamp, beijing_date, beijing_datetime, client_ip, status, version)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [author, content, now, now, beijingDate, beijingDateTime, clientIP, 'active', 1],
                    function(err) {
                        if (err) {
                            return callback(err, null);
                        }

                        const newMessage = {
                            id: this.lastID,
                            author: author,
                            content: content,
                            created_at: now,
                            timestamp: formatTimestamp(now),
                            beijing_date: beijingDate,
                            beijing_datetime: beijingDateTime
                        };

                        callback(null, newMessage);
                    }
                );
            } else {
                // 使用旧表结构
                db.run(
                    'INSERT INTO love_messages (author, content, created_at, updated_at, ip) VALUES (?, ?, ?, ?, ?)',
                    [author, content, now, now, clientIP],
                    function(err) {
                        if (err) {
                            return callback(err, null);
                        }

                        const newMessage = {
                            id: this.lastID,
                            author: author,
                            content: content,
                            created_at: now,
                            timestamp: formatTimestamp(now)
                        };

                        callback(null, newMessage);
                    }
                );
            }
        });
    }

    // 更新留言
    static update(id, content, callback) {
        const trimmedContent = content.trim();
        if (trimmedContent.length === 0) {
            return callback(new Error('留言内容不能为空'), null);
        }
        
        if (trimmedContent.length > 1000) {
            return callback(new Error('留言内容不能超过1000个字符'), null);
        }
        
        const now = getCurrentTimestamp();

        getTableName((tableName) => {
            const query = tableName === 'love_messages_new'
                ? 'UPDATE love_messages_new SET content = ?, updated_timestamp = ? WHERE id = ?'
                : 'UPDATE love_messages SET content = ?, updated_at = ? WHERE id = ?';

            db.run(query, [trimmedContent, now, id], function(err) {
                if (err) {
                    return callback(err, null);
                }

                if (this.changes === 0) {
                    return callback(new Error('留言不存在'), null);
                }

                callback(null, { success: true });
            });
        });
    }

    // 删除留言
    static delete(id, callback) {
        getTableName((tableName) => {
            const query = tableName === 'love_messages_new'
                ? 'DELETE FROM love_messages_new WHERE id = ?'
                : 'DELETE FROM love_messages WHERE id = ?';

            db.run(query, [id], function(err) {
                if (err) {
                    return callback(err, null);
                }

                if (this.changes === 0) {
                    return callback(new Error('留言不存在'), null);
                }

                callback(null, { success: true });
            });
        });
    }

    // 获取统计信息
    static getStats(callback) {
        getTableName((tableName) => {
            const query = tableName === 'love_messages_new'
                ? 'SELECT COUNT(*) as total FROM love_messages_new WHERE status = "active"'
                : 'SELECT COUNT(*) as total FROM love_messages';

            db.get(query, [], (err, row) => {
                if (err) {
                    return callback(err, null);
                }
                
                callback(null, {
                    total_messages: row.total
                });
            });
        });
    }
}

module.exports = Message;
